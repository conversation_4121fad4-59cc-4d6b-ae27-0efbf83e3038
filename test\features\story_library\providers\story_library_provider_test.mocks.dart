// Mocks generated by <PERSON>ckito 5.4.4 from annotations
// in choice_once_upon_a_time/test/features/story_library/providers/story_library_provider_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:choice_once_upon_a_time/features/story_library/data/story_repository.dart'
    as _i2;
import 'package:choice_once_upon_a_time/models/story_metadata_model.dart'
    as _i4;
import 'package:choice_once_upon_a_time/models/story_model.dart' as _i5;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [StoryRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockStoryRepository extends _i1.Mock implements _i2.StoryRepository {
  MockStoryRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<List<_i4.StoryMetadataModel>> fetchStoryMetadataList() =>
      (super.noSuchMethod(
        Invocation.method(
          #fetchStoryMetadataList,
          [],
        ),
        returnValue: _i3.Future<List<_i4.StoryMetadataModel>>.value(
            <_i4.StoryMetadataModel>[]),
      ) as _i3.Future<List<_i4.StoryMetadataModel>>);

  @override
  _i3.Future<_i5.StoryModel?> fetchStoryById(
    String? storyId, {
    String? dataSource = r'auto',
    String? version = r'1.0.0',
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #fetchStoryById,
          [storyId],
          {
            #dataSource: dataSource,
            #version: version,
          },
        ),
        returnValue: _i3.Future<_i5.StoryModel?>.value(),
      ) as _i3.Future<_i5.StoryModel?>);

  @override
  _i3.Future<bool> hasStoryUpdate(
    String? storyId,
    String? currentVersion,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #hasStoryUpdate,
          [
            storyId,
            currentVersion,
          ],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<bool> downloadStoryFromFirebase(
    String? storyId, {
    dynamic Function(double)? onProgress,
    dynamic Function(String)? onStatusUpdate,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #downloadStoryFromFirebase,
          [storyId],
          {
            #onProgress: onProgress,
            #onStatusUpdate: onStatusUpdate,
          },
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<bool> isStoryAvailableForDownload(String? storyId) =>
      (super.noSuchMethod(
        Invocation.method(
          #isStoryAvailableForDownload,
          [storyId],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<Map<String, dynamic>> getStoryStorageInfo(String? storyId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getStoryStorageInfo,
          [storyId],
        ),
        returnValue:
            _i3.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<bool> deleteLocalStory(String? storyId) => (super.noSuchMethod(
        Invocation.method(
          #deleteLocalStory,
          [storyId],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);
}
