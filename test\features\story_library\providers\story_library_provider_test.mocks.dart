// Mocks generated by <PERSON><PERSON>to 5.4.4 from annotations
// in choice_once_upon_a_time/test/features/story_library/providers/story_library_provider_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:choice_once_upon_a_time/features/story_library/data/story_repository.dart'
    as _i3;
import 'package:choice_once_upon_a_time/models/story_metadata_model.dart'
    as _i5;
import 'package:choice_once_upon_a_time/models/story_model.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeStoryModel_0 extends _i1.SmartFake implements _i2.StoryModel {
  _FakeStoryModel_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [StoryRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockStoryRepository extends _i1.Mock implements _i3.StoryRepository {
  MockStoryRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<List<_i5.StoryMetadataModel>> fetchStoryMetadataList() =>
      (super.noSuchMethod(
        Invocation.method(
          #fetchStoryMetadataList,
          [],
        ),
        returnValue: _i4.Future<List<_i5.StoryMetadataModel>>.value(
            <_i5.StoryMetadataModel>[]),
      ) as _i4.Future<List<_i5.StoryMetadataModel>>);

  @override
  _i4.Future<_i2.StoryModel> fetchStoryById(String? storyId) =>
      (super.noSuchMethod(
        Invocation.method(
          #fetchStoryById,
          [storyId],
        ),
        returnValue: _i4.Future<_i2.StoryModel>.value(_FakeStoryModel_0(
          this,
          Invocation.method(
            #fetchStoryById,
            [storyId],
          ),
        )),
      ) as _i4.Future<_i2.StoryModel>);

  @override
  _i4.Future<bool> storyExists(String? storyId) => (super.noSuchMethod(
        Invocation.method(
          #storyExists,
          [storyId],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  void clearCache() => super.noSuchMethod(
        Invocation.method(
          #clearCache,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  Map<String, dynamic> getCacheStats() => (super.noSuchMethod(
        Invocation.method(
          #getCacheStats,
          [],
        ),
        returnValue: <String, dynamic>{},
      ) as Map<String, dynamic>);
}
