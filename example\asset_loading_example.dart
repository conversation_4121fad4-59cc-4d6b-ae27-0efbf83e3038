import 'package:flutter/material.dart';
import 'package:choice_once_upon_a_time/core/services/asset_story_service.dart';
import 'package:choice_once_upon_a_time/core/services/asset_fallback_service.dart';
import 'package:choice_once_upon_a_time/features/story_library/data/story_repository.dart';
import 'package:choice_once_upon_a_time/models/story_model.dart';
import 'package:choice_once_upon_a_time/models/story_metadata_model.dart';

/// Example demonstrating the complete asset loading workflow
/// 
/// This example shows how to:
/// 1. Discover available stories from assets
/// 2. Load story metadata for the library
/// 3. Load complete stories with priority loading
/// 4. Handle missing assets with fallbacks
/// 5. Validate story structures
class AssetLoadingExample extends StatefulWidget {
  const AssetLoadingExample({super.key});

  @override
  State<AssetLoadingExample> createState() => _AssetLoadingExampleState();
}

class _AssetLoadingExampleState extends State<AssetLoadingExample> {
  final AssetStoryService _assetStoryService = AssetStoryService();
  final AssetFallbackService _assetFallbackService = AssetFallbackService();
  final StoryRepository _storyRepository = StoryRepository();

  List<String> _availableStoryIds = [];
  List<StoryMetadataModel> _storyMetadata = [];
  StoryModel? _loadedStory;
  Map<String, dynamic>? _validationResult;
  String _status = 'Ready';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    setState(() {
      _status = 'Initializing services...';
      _isLoading = true;
    });

    try {
      // Preload placeholder assets
      await _assetFallbackService.preloadPlaceholders();
      
      // Validate placeholder assets
      final placeholdersValid = await _assetFallbackService.validatePlaceholderAssets();
      
      setState(() {
        _status = placeholdersValid 
            ? 'Services initialized successfully'
            : 'Warning: Some placeholder assets missing';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _status = 'Failed to initialize services: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _discoverStories() async {
    setState(() {
      _status = 'Discovering available stories...';
      _isLoading = true;
    });

    try {
      final storyIds = await _assetStoryService.getAvailableStoryIds();
      
      setState(() {
        _availableStoryIds = storyIds;
        _status = 'Found ${storyIds.length} stories: ${storyIds.join(', ')}';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _status = 'Failed to discover stories: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _loadStoryMetadata() async {
    setState(() {
      _status = 'Loading story metadata...';
      _isLoading = true;
    });

    try {
      final metadata = await _storyRepository.fetchStoryMetadataList();
      final assetMetadata = metadata.where((story) => story.dataSource == 'asset').toList();
      
      setState(() {
        _storyMetadata = assetMetadata;
        _status = 'Loaded metadata for ${assetMetadata.length} asset-based stories';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _status = 'Failed to load metadata: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _loadStoryExample(String storyId) async {
    setState(() {
      _status = 'Loading story: $storyId...';
      _isLoading = true;
    });

    try {
      // Demonstrate priority loading through repository
      final story = await _storyRepository.fetchStoryById(storyId);
      
      setState(() {
        _loadedStory = story;
        _status = story != null 
            ? 'Successfully loaded story: ${story.title}'
            : 'Story not found: $storyId';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _status = 'Failed to load story: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _validateStoryAssets(String storyId) async {
    setState(() {
      _status = 'Validating assets for: $storyId...';
      _isLoading = true;
    });

    try {
      final validation = await _assetStoryService.validateStoryAssets(storyId);
      
      setState(() {
        _validationResult = validation;
        _status = 'Validation complete for: $storyId';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _status = 'Failed to validate assets: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _testAssetFallbacks() async {
    setState(() {
      _status = 'Testing asset fallbacks...';
      _isLoading = true;
    });

    try {
      // Test image fallback
      final imageFallback = await _assetFallbackService.getImageOrPlaceholder('non_existent_image.jpg');
      
      // Test cover fallback
      final coverFallback = await _assetFallbackService.getCoverImageOrPlaceholder('non_existent_cover.jpg');
      
      // Test asset info
      final assetInfo = await _assetFallbackService.getAssetInfo('test_asset.jpg', 'image');
      
      setState(() {
        _status = 'Fallback tests complete:\n'
            'Image: $imageFallback\n'
            'Cover: $coverFallback\n'
            'Asset exists: ${assetInfo['exists']}';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _status = 'Failed to test fallbacks: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Asset Loading Example'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status display
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceVariant,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'Status: $_status',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Action buttons
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton(
                  onPressed: _isLoading ? null : _discoverStories,
                  child: const Text('Discover Stories'),
                ),
                ElevatedButton(
                  onPressed: _isLoading ? null : _loadStoryMetadata,
                  child: const Text('Load Metadata'),
                ),
                ElevatedButton(
                  onPressed: _isLoading ? null : _testAssetFallbacks,
                  child: const Text('Test Fallbacks'),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Results display
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Available story IDs
                    if (_availableStoryIds.isNotEmpty) ...[
                      Text(
                        'Available Stories:',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      ...(_availableStoryIds.map((storyId) => Card(
                        child: ListTile(
                          title: Text(storyId),
                          trailing: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              IconButton(
                                icon: const Icon(Icons.play_arrow),
                                onPressed: () => _loadStoryExample(storyId),
                                tooltip: 'Load Story',
                              ),
                              IconButton(
                                icon: const Icon(Icons.check_circle),
                                onPressed: () => _validateStoryAssets(storyId),
                                tooltip: 'Validate Assets',
                              ),
                            ],
                          ),
                        ),
                      ))),
                      const SizedBox(height: 16),
                    ],
                    
                    // Story metadata
                    if (_storyMetadata.isNotEmpty) ...[
                      Text(
                        'Story Metadata:',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      ...(_storyMetadata.map((metadata) => Card(
                        child: ListTile(
                          title: Text(metadata.title['en-US'] ?? metadata.id),
                          subtitle: Text('Moral: ${metadata.targetMoralValue}'),
                          trailing: metadata.rewards != null 
                              ? const Icon(Icons.star, color: Colors.amber)
                              : null,
                        ),
                      ))),
                      const SizedBox(height: 16),
                    ],
                    
                    // Loaded story details
                    if (_loadedStory != null) ...[
                      Text(
                        'Loaded Story Details:',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text('ID: ${_loadedStory!.id}'),
                              Text('Title: ${_loadedStory!.title}'),
                              Text('Moral: ${_loadedStory!.targetMoralValue}'),
                              Text('Scenes: ${_loadedStory!.scenes.length}'),
                              if (_loadedStory!.rewards != null) ...[
                                Text('Completion Reward: ${_loadedStory!.rewards!.completion}'),
                                Text('Moral Rewards: ${_loadedStory!.rewards!.moralChoices.join(', ')}'),
                              ],
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                    ],
                    
                    // Validation results
                    if (_validationResult != null) ...[
                      Text(
                        'Asset Validation:',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text('Story ID: ${_validationResult!['storyId']}'),
                              Text('Has story.json: ${_validationResult!['hasStoryJson']}'),
                              Text('Has images: ${_validationResult!['hasImagesFolder']}'),
                              Text('Has audio: ${_validationResult!['hasAudioFolder']}'),
                              Text('Is valid: ${_validationResult!['isValid']}'),
                              if (_validationResult!['missingAssets'].isNotEmpty)
                                Text('Missing: ${_validationResult!['missingAssets'].join(', ')}'),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _assetStoryService.clearCache();
    super.dispose();
  }
}
