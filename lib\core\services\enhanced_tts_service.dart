import 'package:flutter_tts/flutter_tts.dart';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';

/// Enhanced TTS service with emotion-based voice modulation
class EnhancedTtsService {
  static final EnhancedTtsService _instance = EnhancedTtsService._internal();
  factory EnhancedTtsService() => _instance;
  EnhancedTtsService._internal();

  final FlutterTts _flutterTts = FlutterTts();
  final Logger _logger = Logger();
  
  bool _isInitialized = false;
  bool _isPlaying = false;
  String? _currentLanguage;

  /// Initializes the TTS service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _logger.i('[EnhancedTtsService] Initializing TTS service');

      // Set default language
      await _flutterTts.setLanguage('en-US');
      _currentLanguage = 'en-US';

      // Set default speech rate and pitch
      await _flutterTts.setSpeechRate(0.5);
      await _flutterTts.setPitch(1.0);
      await _flutterTts.setVolume(1.0);

      // Set up completion handlers
      _flutterTts.setCompletionHandler(() {
        _isPlaying = false;
        _logger.d('[EnhancedTtsService] TTS playback completed');
      });

      _flutterTts.setErrorHandler((message) {
        _isPlaying = false;
        _logger.e('[EnhancedTtsService] TTS error: $message');
      });

      _isInitialized = true;
      _logger.i('[EnhancedTtsService] TTS service initialized successfully');

    } catch (e) {
      _logger.e('[EnhancedTtsService] Failed to initialize TTS: $e');
      throw TtsException('Failed to initialize TTS service', details: e.toString());
    }
  }

  /// Speaks text with emotion-based voice modulation
  /// 
  /// [text] - The text to speak
  /// [emotionCue] - The emotion to apply (e.g., "happy", "sad", "excited")
  /// [language] - Optional language code (defaults to current language)
  /// 
  /// Returns a Future that completes when speaking starts
  Future<void> speakWithEmotion(
    String text, {
    String? emotionCue,
    String? language,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      _logger.i('[EnhancedTtsService] Speaking with emotion: $emotionCue');

      // Stop any current playback
      await stop();

      // Set language if provided
      if (language != null && language != _currentLanguage) {
        await _flutterTts.setLanguage(language);
        _currentLanguage = language;
      }

      // Apply emotion-based voice settings
      await _applyEmotionSettings(emotionCue);

      // Start speaking
      _isPlaying = true;
      await _flutterTts.speak(text);

    } catch (e) {
      _isPlaying = false;
      _logger.e('[EnhancedTtsService] Failed to speak text: $e');
      throw TtsException('Failed to speak text', details: e.toString());
    }
  }

  /// Applies voice settings based on emotion cue
  Future<void> _applyEmotionSettings(String? emotionCue) async {
    if (emotionCue == null) {
      // Default neutral settings
      await _flutterTts.setSpeechRate(0.5);
      await _flutterTts.setPitch(1.0);
      return;
    }

    switch (emotionCue.toLowerCase()) {
      case 'happy':
      case 'cheerful':
      case 'joyful':
        await _flutterTts.setSpeechRate(0.6); // Slightly faster
        await _flutterTts.setPitch(1.2); // Higher pitch
        break;

      case 'excited':
      case 'enthusiastic':
        await _flutterTts.setSpeechRate(0.7); // Faster
        await _flutterTts.setPitch(1.3); // Higher pitch
        break;

      case 'sad':
      case 'melancholy':
      case 'disappointed':
        await _flutterTts.setSpeechRate(0.4); // Slower
        await _flutterTts.setPitch(0.8); // Lower pitch
        break;

      case 'angry':
      case 'frustrated':
        await _flutterTts.setSpeechRate(0.6); // Moderate speed
        await _flutterTts.setPitch(0.9); // Slightly lower pitch
        break;

      case 'surprised':
      case 'amazed':
        await _flutterTts.setSpeechRate(0.5); // Normal speed
        await _flutterTts.setPitch(1.4); // Much higher pitch
        break;

      case 'gentle':
      case 'calm':
      case 'peaceful':
        await _flutterTts.setSpeechRate(0.4); // Slower
        await _flutterTts.setPitch(1.0); // Normal pitch
        break;

      case 'mysterious':
      case 'spooky':
        await _flutterTts.setSpeechRate(0.3); // Much slower
        await _flutterTts.setPitch(0.7); // Lower pitch
        break;

      case 'urgent':
      case 'worried':
        await _flutterTts.setSpeechRate(0.7); // Faster
        await _flutterTts.setPitch(1.1); // Slightly higher pitch
        break;

      default:
        // Default neutral settings for unknown emotions
        await _flutterTts.setSpeechRate(0.5);
        await _flutterTts.setPitch(1.0);
        _logger.w('[EnhancedTtsService] Unknown emotion cue: $emotionCue, using neutral settings');
    }
  }

  /// Speaks plain text without emotion modulation
  Future<void> speak(String text, {String? language}) async {
    await speakWithEmotion(text, language: language);
  }

  /// Pauses the current TTS playback
  Future<void> pause() async {
    if (_isPlaying) {
      await _flutterTts.pause();
      _logger.d('[EnhancedTtsService] TTS paused');
    }
  }

  /// Stops the current TTS playback
  Future<void> stop() async {
    if (_isPlaying) {
      await _flutterTts.stop();
      _isPlaying = false;
      _logger.d('[EnhancedTtsService] TTS stopped');
    }
  }

  /// Checks if TTS is currently playing
  bool get isPlaying => _isPlaying;

  /// Gets available languages
  Future<List<String>> getAvailableLanguages() async {
    try {
      final languages = await _flutterTts.getLanguages;
      return List<String>.from(languages);
    } catch (e) {
      _logger.e('[EnhancedTtsService] Failed to get available languages: $e');
      return ['en-US']; // Fallback to English
    }
  }

  /// Sets the speech rate (0.0 to 1.0)
  Future<void> setSpeechRate(double rate) async {
    await _flutterTts.setSpeechRate(rate.clamp(0.0, 1.0));
  }

  /// Sets the pitch (0.5 to 2.0)
  Future<void> setPitch(double pitch) async {
    await _flutterTts.setPitch(pitch.clamp(0.5, 2.0));
  }

  /// Sets the volume (0.0 to 1.0)
  Future<void> setVolume(double volume) async {
    await _flutterTts.setVolume(volume.clamp(0.0, 1.0));
  }

  /// Gets the current language
  String? get currentLanguage => _currentLanguage;

  /// Disposes of the TTS service
  Future<void> dispose() async {
    await stop();
    _isInitialized = false;
    _logger.i('[EnhancedTtsService] TTS service disposed');
  }
}

/// Custom exception for TTS operations
class TtsException implements Exception {
  final String message;
  final String? details;

  const TtsException(this.message, {this.details});

  @override
  String toString() {
    if (details != null) {
      return 'TtsException: $message\nDetails: $details';
    }
    return 'TtsException: $message';
  }
}
