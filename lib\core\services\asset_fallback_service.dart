import 'dart:io';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import 'package:choice_once_upon_a_time/core/services/enhanced_tts_service.dart';

/// Service for handling missing assets with graceful fallbacks
class AssetFallbackService {
  static final AssetFallbackService _instance = AssetFallbackService._internal();
  factory AssetFallbackService() => _instance;
  AssetFallbackService._internal();

  final Logger _logger = Logger();
  final EnhancedTtsService _ttsService = EnhancedTtsService();

  // Placeholder asset paths
  static const String _placeholderImagePath = 'assets/images/placeholder.jpg';
  static const String _placeholderCoverPath = 'assets/images/story_covers/placeholder_cover.jpg';
  static const String _placeholderScenePath = 'assets/images/placeholder_scene.jpg';

  /// Checks if an asset exists and returns the path or a placeholder
  /// 
  /// [assetPath] - The original asset path to check
  /// [assetType] - Type of asset: 'image', 'audio', 'cover'
  /// 
  /// Returns the original path if it exists, otherwise a placeholder path
  Future<String> getAssetOrFallback(String assetPath, String assetType) async {
    try {
      // Check if the asset exists
      if (await _assetExists(assetPath)) {
        _logger.d('[AssetFallbackService] Asset exists: $assetPath');
        return assetPath;
      }

      // Return appropriate placeholder
      final placeholderPath = _getPlaceholderPath(assetType);
      _logger.w('[AssetFallbackService] Asset not found, using placeholder: $assetPath -> $placeholderPath');
      return placeholderPath;

    } catch (e) {
      _logger.e('[AssetFallbackService] Error checking asset: $assetPath - $e');
      return _getPlaceholderPath(assetType);
    }
  }

  /// Checks if an image asset exists, returns path or placeholder
  Future<String> getImageOrPlaceholder(String imagePath) async {
    return await getAssetOrFallback(imagePath, 'image');
  }

  /// Checks if a cover image exists, returns path or placeholder
  Future<String> getCoverImageOrPlaceholder(String coverPath) async {
    return await getAssetOrFallback(coverPath, 'cover');
  }

  /// Handles audio playback with TTS fallback
  /// 
  /// [audioPath] - The original audio file path
  /// [fallbackText] - Text to speak if audio is missing
  /// [emotionCue] - Emotion cue for TTS
  /// [language] - Language for TTS
  /// 
  /// Returns true if audio was played, false if TTS was used
  Future<bool> playAudioOrTts(
    String audioPath,
    String fallbackText, {
    String? emotionCue,
    String? language,
  }) async {
    try {
      // Check if audio file exists
      if (await _assetExists(audioPath)) {
        _logger.d('[AssetFallbackService] Audio exists: $audioPath');
        // Audio exists, let the audio player handle it
        return true;
      }

      // Audio doesn't exist, use TTS
      _logger.w('[AssetFallbackService] Audio not found, using TTS: $audioPath');
      await _ttsService.speakWithEmotion(
        fallbackText,
        emotionCue: emotionCue,
        language: language,
      );
      return false;

    } catch (e) {
      _logger.e('[AssetFallbackService] Error with audio/TTS: $audioPath - $e');
      
      // Fallback to TTS without emotion
      try {
        await _ttsService.speak(fallbackText, language: language);
        return false;
      } catch (ttsError) {
        _logger.e('[AssetFallbackService] TTS also failed: $ttsError');
        return false;
      }
    }
  }

  /// Checks if a local file exists (for downloaded stories)
  Future<bool> localFileExists(String filePath) async {
    try {
      final file = File(filePath);
      return await file.exists();
    } catch (e) {
      _logger.e('[AssetFallbackService] Error checking local file: $filePath - $e');
      return false;
    }
  }

  /// Gets a local file path or fallback for downloaded stories
  Future<String> getLocalFileOrFallback(String localPath, String assetType) async {
    try {
      if (await localFileExists(localPath)) {
        _logger.d('[AssetFallbackService] Local file exists: $localPath');
        return localPath;
      }

      // Return appropriate placeholder
      final placeholderPath = _getPlaceholderPath(assetType);
      _logger.w('[AssetFallbackService] Local file not found, using placeholder: $localPath -> $placeholderPath');
      return placeholderPath;

    } catch (e) {
      _logger.e('[AssetFallbackService] Error checking local file: $localPath - $e');
      return _getPlaceholderPath(assetType);
    }
  }

  /// Validates that all required placeholder assets exist
  Future<bool> validatePlaceholderAssets() async {
    final requiredAssets = [
      _placeholderImagePath,
      _placeholderCoverPath,
      _placeholderScenePath,
    ];

    for (final assetPath in requiredAssets) {
      if (!await _assetExists(assetPath)) {
        _logger.e('[AssetFallbackService] Missing required placeholder: $assetPath');
        return false;
      }
    }

    _logger.i('[AssetFallbackService] All placeholder assets validated');
    return true;
  }

  /// Creates placeholder assets if they don't exist
  Future<void> ensurePlaceholderAssets() async {
    try {
      // Check if placeholders exist
      final isValid = await validatePlaceholderAssets();
      if (isValid) {
        return;
      }

      _logger.w('[AssetFallbackService] Some placeholder assets are missing');
      
      // In a real implementation, you might want to create default placeholder images
      // For now, we'll just log the issue
      _logger.w('[AssetFallbackService] Please ensure placeholder assets are included in the app bundle');

    } catch (e) {
      _logger.e('[AssetFallbackService] Error ensuring placeholder assets: $e');
    }
  }

  /// Gets the appropriate placeholder path for an asset type
  String _getPlaceholderPath(String assetType) {
    switch (assetType.toLowerCase()) {
      case 'cover':
        return _placeholderCoverPath;
      case 'scene':
        return _placeholderScenePath;
      case 'image':
      default:
        return _placeholderImagePath;
    }
  }

  /// Checks if an asset exists in the bundle
  Future<bool> _assetExists(String assetPath) async {
    try {
      await rootBundle.load(assetPath);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Gets asset information including existence and fallback status
  Future<Map<String, dynamic>> getAssetInfo(String assetPath, String assetType) async {
    final exists = await _assetExists(assetPath);
    final fallbackPath = exists ? assetPath : _getPlaceholderPath(assetType);
    
    return {
      'originalPath': assetPath,
      'exists': exists,
      'fallbackPath': fallbackPath,
      'isUsingFallback': !exists,
      'assetType': assetType,
    };
  }

  /// Preloads critical placeholder assets
  Future<void> preloadPlaceholders() async {
    try {
      _logger.i('[AssetFallbackService] Preloading placeholder assets');

      final placeholders = [
        _placeholderImagePath,
        _placeholderCoverPath,
        _placeholderScenePath,
      ];

      for (final placeholder in placeholders) {
        try {
          await rootBundle.load(placeholder);
          _logger.d('[AssetFallbackService] Preloaded: $placeholder');
        } catch (e) {
          _logger.w('[AssetFallbackService] Failed to preload: $placeholder - $e');
        }
      }

      _logger.i('[AssetFallbackService] Placeholder preloading completed');

    } catch (e) {
      _logger.e('[AssetFallbackService] Error preloading placeholders: $e');
    }
  }

  /// Disposes of the service
  Future<void> dispose() async {
    await _ttsService.dispose();
    _logger.i('[AssetFallbackService] Service disposed');
  }
}

/// Exception for asset fallback operations
class AssetFallbackException implements Exception {
  final String message;
  final String? details;

  const AssetFallbackException(this.message, {this.details});

  @override
  String toString() {
    if (details != null) {
      return 'AssetFallbackException: $message\nDetails: $details';
    }
    return 'AssetFallbackException: $message';
  }
}
