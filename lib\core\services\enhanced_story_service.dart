import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:logger/logger.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart';
import 'package:choice_once_upon_a_time/models/story_metadata_model.dart';

/// Enhanced service for loading stories with new JSON structure
class EnhancedStoryService {
  static final Logger _logger = Logger();
  
  // Cache for loaded stories and metadata
  final Map<String, EnhancedStoryModel> _storyCache = {};
  final Map<String, StoryMetadataModel> _metadataCache = {};
  List<StoryMetadataModel>? _allStoriesCache;

  /// Scans for all available stories in assets/stories/
  Future<List<String>> scanAvailableStories() async {
    try {
      _logger.i('[EnhancedStoryService] Scanning for available stories');
      
      // Get the asset manifest to find story folders
      final manifestContent = await rootBundle.loadString('AssetManifest.json');
      final Map<String, dynamic> manifestMap = jsonDecode(manifestContent);
      
      final storyIds = <String>{};
      
      // Look for story JSON files in the pattern: assets/stories/<story_id>/<story_id>.json
      for (final assetPath in manifestMap.keys) {
        if (assetPath.startsWith('assets/stories/') && assetPath.endsWith('.json')) {
          final pathParts = assetPath.split('/');
          if (pathParts.length >= 4) {
            final storyId = pathParts[2]; // Extract story_id from path
            final fileName = pathParts[3];
            
            // Check if it follows the pattern <story_id>/<story_id>.json
            if (fileName == '$storyId.json') {
              storyIds.add(storyId);
            }
          }
        }
      }
      
      final storyList = storyIds.toList()..sort();
      _logger.i('[EnhancedStoryService] Found ${storyList.length} stories: $storyList');
      return storyList;
      
    } catch (e) {
      _logger.e('[EnhancedStoryService] Failed to scan stories: $e');
      return [];
    }
  }

  /// Loads all story metadata
  Future<List<StoryMetadataModel>> getAllStoryMetadata() async {
    if (_allStoriesCache != null) {
      return _allStoriesCache!;
    }

    try {
      _logger.i('[EnhancedStoryService] Loading all story metadata');
      
      final storyIds = await scanAvailableStories();
      final metadataList = <StoryMetadataModel>[];
      
      for (final storyId in storyIds) {
        try {
          final story = await loadStory(storyId);
          if (story != null) {
            final metadata = _createMetadataFromStory(story);
            metadataList.add(metadata);
            _metadataCache[storyId] = metadata;
          }
        } catch (e) {
          _logger.w('[EnhancedStoryService] Failed to load metadata for story $storyId: $e');
        }
      }
      
      _allStoriesCache = metadataList;
      _logger.i('[EnhancedStoryService] Loaded ${metadataList.length} story metadata entries');
      return metadataList;
      
    } catch (e) {
      _logger.e('[EnhancedStoryService] Failed to load story metadata: $e');
      return [];
    }
  }

  /// Loads a specific story by ID
  Future<EnhancedStoryModel?> loadStory(String storyId) async {
    if (_storyCache.containsKey(storyId)) {
      return _storyCache[storyId];
    }

    try {
      _logger.i('[EnhancedStoryService] Loading story: $storyId');
      
      // Try to load from the story's folder with pattern <story_id>/<story_id>.json
      final storyPath = 'assets/stories/$storyId/$storyId.json';
      final jsonString = await rootBundle.loadString(storyPath);
      final Map<String, dynamic> storyJson = jsonDecode(jsonString);
      
      // Validate the story structure
      if (!_validateStoryStructure(storyJson, storyId)) {
        throw Exception('Invalid story structure for $storyId');
      }
      
      final story = EnhancedStoryModel.fromJson(storyJson);
      _storyCache[storyId] = story;
      
      _logger.i('[EnhancedStoryService] Successfully loaded story: $storyId');
      return story;
      
    } catch (e) {
      _logger.e('[EnhancedStoryService] Failed to load story $storyId: $e');
      return null;
    }
  }

  /// Validates story structure and required assets
  bool _validateStoryStructure(Map<String, dynamic> storyJson, String storyId) {
    try {
      // Check required fields
      final requiredFields = ['story_id', 'title', 'moral', 'setup', 'narrator_profile', 'characters', 'scenes'];
      for (final field in requiredFields) {
        if (!storyJson.containsKey(field)) {
          _logger.w('[EnhancedStoryService] Missing required field: $field in story $storyId');
          return false;
        }
      }
      
      // Validate story_id matches folder name
      if (storyJson['story_id'] != storyId) {
        _logger.w('[EnhancedStoryService] Story ID mismatch: expected $storyId, got ${storyJson['story_id']}');
        return false;
      }
      
      // Validate scenes structure
      final scenes = storyJson['scenes'] as List<dynamic>?;
      if (scenes == null || scenes.isEmpty) {
        _logger.w('[EnhancedStoryService] No scenes found in story $storyId');
        return false;
      }
      
      // Check if required assets exist (cover image, scene images)
      return _validateAssets(storyJson, storyId);
      
    } catch (e) {
      _logger.e('[EnhancedStoryService] Error validating story structure: $e');
      return false;
    }
  }

  /// Validates that required assets exist
  bool _validateAssets(Map<String, dynamic> storyJson, String storyId) {
    try {
      // For now, we'll assume assets exist if the JSON is valid
      // In a production app, you might want to check if image files actually exist
      return true;
    } catch (e) {
      _logger.e('[EnhancedStoryService] Error validating assets: $e');
      return false;
    }
  }

  /// Creates StoryMetadataModel from EnhancedStoryModel
  StoryMetadataModel _createMetadataFromStory(EnhancedStoryModel story) {
    return StoryMetadataModel(
      id: story.storyId,
      title: {'en-US': story.title},
      coverImageUrl: story.coverImagePath,
      loglineShort: {'en-US': story.setup.context},
      targetMoralValue: story.moral,
      targetAgeSubSegment: story.ageGroup,
      estimatedDurationMinutes: _estimateStoryDuration(story),
      isFree: true,
      published: true,
      dataSource: 'enhanced_asset',
      version: '1.0.0',
      supportedLanguages: ['en-US'],
      defaultLanguage: 'en-US',
      initialSceneId: story.initialScene.id,
    );
  }

  /// Estimates story duration based on scene count and text length
  int _estimateStoryDuration(EnhancedStoryModel story) {
    // Rough estimate: 30 seconds per scene + reading time
    int totalDuration = story.scenes.length * 30;
    
    // Add time for text reading (average 150 words per minute)
    for (final scene in story.scenes) {
      final wordCount = scene.text.split(' ').length;
      totalDuration += (wordCount / 150 * 60).round();
    }
    
    // Convert to minutes and ensure minimum of 2 minutes
    return (totalDuration / 60).round().clamp(2, 30);
  }

  /// Gets character profiles for a story
  Future<List<CharacterModel>> getCharacterProfiles(String storyId) async {
    final story = await loadStory(storyId);
    return story?.characters ?? [];
  }

  /// Gets story setup information
  Future<StorySetupModel?> getStorySetup(String storyId) async {
    final story = await loadStory(storyId);
    return story?.setup;
  }

  /// Gets narrator profile for a story
  Future<NarratorProfileModel?> getNarratorProfile(String storyId) async {
    final story = await loadStory(storyId);
    return story?.narratorProfile;
  }

  /// Gets post-story content
  Future<PostStoryModel?> getPostStoryContent(String storyId) async {
    final story = await loadStory(storyId);
    return story?.postStory;
  }

  /// Checks if a story exists
  Future<bool> storyExists(String storyId) async {
    try {
      final story = await loadStory(storyId);
      return story != null;
    } catch (e) {
      return false;
    }
  }

  /// Clears all caches
  void clearCache() {
    _storyCache.clear();
    _metadataCache.clear();
    _allStoriesCache = null;
    _logger.i('[EnhancedStoryService] Cache cleared');
  }

  /// Gets cache statistics
  Map<String, dynamic> getCacheStats() {
    return {
      'storiesLoaded': _storyCache.length,
      'metadataCached': _metadataCache.length,
      'allStoriesCached': _allStoriesCache != null,
    };
  }
}
