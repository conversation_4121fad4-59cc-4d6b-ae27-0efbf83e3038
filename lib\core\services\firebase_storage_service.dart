import 'dart:io';
import 'dart:typed_data';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';

/// Service for downloading story files from Firebase Storage
class FirebaseStorageService {
  static final FirebaseStorageService _instance = FirebaseStorageService._internal();
  factory FirebaseStorageService() => _instance;
  FirebaseStorageService._internal();

  final FirebaseStorage _storage = FirebaseStorage.instance;
  final Logger _logger = Logger();

  /// Downloads a story ZIP file from Firebase Storage
  /// 
  /// [storyId] - The ID of the story to download
  /// [onProgress] - Optional callback for download progress (0.0 to 1.0)
  /// 
  /// Returns the path to the downloaded ZIP file
  Future<String> downloadStoryZip(
    String storyId, {
    Function(double progress)? onProgress,
  }) async {
    try {
      _logger.i('[FirebaseStorageService] Starting download for story: $storyId');

      // Get reference to the story ZIP file
      final ref = _storage.ref().child('stories/$storyId.zip');
      
      // Check if file exists
      try {
        await ref.getMetadata();
      } catch (e) {
        throw FirebaseStorageException(
          'Story ZIP file not found in Firebase Storage: $storyId',
          details: e.toString(),
        );
      }

      // Get temporary directory
      final tempDir = await getTemporaryDirectory();
      final zipFile = File('${tempDir.path}/$storyId.zip');

      // Ensure parent directory exists
      await zipFile.parent.create(recursive: true);

      // Download the file with progress tracking
      final downloadTask = ref.writeToFile(zipFile);
      
      if (onProgress != null) {
        downloadTask.snapshotEvents.listen((taskSnapshot) {
          final progress = taskSnapshot.bytesTransferred / taskSnapshot.totalBytes;
          onProgress(progress);
        });
      }

      await downloadTask;

      _logger.i('[FirebaseStorageService] Successfully downloaded story ZIP: ${zipFile.path}');
      return zipFile.path;

    } catch (e) {
      _logger.e('[FirebaseStorageService] Failed to download story $storyId: $e');
      if (e is FirebaseStorageException) {
        rethrow;
      }
      throw FirebaseStorageException(
        'Failed to download story from Firebase Storage',
        details: e.toString(),
      );
    }
  }

  /// Downloads story metadata from Firebase Storage
  /// 
  /// [storyId] - The ID of the story
  /// 
  /// Returns the story metadata as a Map
  Future<Map<String, dynamic>?> downloadStoryMetadata(String storyId) async {
    try {
      _logger.i('[FirebaseStorageService] Downloading metadata for story: $storyId');

      final ref = _storage.ref().child('stories/$storyId/story.json');
      
      // Check if file exists
      try {
        await ref.getMetadata();
      } catch (e) {
        _logger.w('[FirebaseStorageService] Story metadata not found: $storyId');
        return null;
      }

      // Download the metadata
      final Uint8List? data = await ref.getData();
      if (data == null) {
        throw FirebaseStorageException('Failed to download story metadata');
      }

      final jsonString = String.fromCharCodes(data);
      _logger.i('[FirebaseStorageService] Successfully downloaded metadata for story: $storyId');
      
      // Parse and return JSON (parsing will be done by the caller)
      return {'jsonString': jsonString};

    } catch (e) {
      _logger.e('[FirebaseStorageService] Failed to download metadata for $storyId: $e');
      if (e is FirebaseStorageException) {
        rethrow;
      }
      throw FirebaseStorageException(
        'Failed to download story metadata from Firebase Storage',
        details: e.toString(),
      );
    }
  }

  /// Checks if a story exists in Firebase Storage
  /// 
  /// [storyId] - The ID of the story to check
  /// 
  /// Returns true if the story ZIP file exists
  Future<bool> storyExists(String storyId) async {
    try {
      final ref = _storage.ref().child('stories/$storyId.zip');
      await ref.getMetadata();
      return true;
    } catch (e) {
      _logger.d('[FirebaseStorageService] Story $storyId does not exist in Firebase Storage');
      return false;
    }
  }

  /// Gets the download URL for a story asset
  /// 
  /// [storyId] - The ID of the story
  /// [assetPath] - The relative path to the asset within the story
  /// 
  /// Returns the download URL for the asset
  Future<String?> getAssetDownloadUrl(String storyId, String assetPath) async {
    try {
      final ref = _storage.ref().child('stories/$storyId/$assetPath');
      return await ref.getDownloadURL();
    } catch (e) {
      _logger.w('[FirebaseStorageService] Failed to get download URL for $storyId/$assetPath: $e');
      return null;
    }
  }

  /// Lists all available stories in Firebase Storage
  /// 
  /// Returns a list of story IDs
  Future<List<String>> listAvailableStories() async {
    try {
      _logger.i('[FirebaseStorageService] Listing available stories');

      final ref = _storage.ref().child('stories');
      final result = await ref.listAll();

      final storyIds = <String>[];
      for (final item in result.items) {
        final fileName = item.name;
        if (fileName.endsWith('.zip')) {
          final storyId = fileName.substring(0, fileName.length - 4); // Remove .zip extension
          storyIds.add(storyId);
        }
      }

      _logger.i('[FirebaseStorageService] Found ${storyIds.length} stories in Firebase Storage');
      return storyIds;

    } catch (e) {
      _logger.e('[FirebaseStorageService] Failed to list available stories: $e');
      throw FirebaseStorageException(
        'Failed to list available stories from Firebase Storage',
        details: e.toString(),
      );
    }
  }
}

/// Custom exception for Firebase Storage operations
class FirebaseStorageException implements Exception {
  final String message;
  final String? details;

  const FirebaseStorageException(this.message, {this.details});

  @override
  String toString() {
    if (details != null) {
      return 'FirebaseStorageException: $message\nDetails: $details';
    }
    return 'FirebaseStorageException: $message';
  }
}
