import 'dart:convert';
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/services.dart';
import 'package:logger/logger.dart';
import 'package:choice_once_upon_a_time/models/story_metadata_model.dart';
import 'package:choice_once_upon_a_time/models/story_model.dart';
import 'package:choice_once_upon_a_time/core/storage/offline_storage_service_mobile.dart';
import 'package:choice_once_upon_a_time/core/services/firebase_storage_service.dart';
import 'package:choice_once_upon_a_time/core/services/zip_extraction_service.dart';
import 'package:choice_once_upon_a_time/core/services/asset_story_service.dart';

class StoryLoadException implements Exception {
  final String message;
  final String? technicalDetails;

  StoryLoadException(this.message, {this.technicalDetails});

  @override
  String toString() {
    if (technicalDetails != null) {
      return '''StoryLoadException: $message
Details: $technicalDetails''';
    }
    return 'StoryLoadException: $message';
  }
}

/// Repository for managing story data from multiple sources
class StoryRepository {
  final FirebaseFirestore _firestore;
  final OfflineStorageService _offlineStorage;
  final FirebaseStorageService _firebaseStorage;
  final ZipExtractionService _zipExtraction;
  final AssetStoryService _assetStoryService;
  final Logger _logger;

  StoryRepository({
    FirebaseFirestore? firestore,
    OfflineStorageService? offlineStorage,
    FirebaseStorageService? firebaseStorage,
    ZipExtractionService? zipExtraction,
    AssetStoryService? assetStoryService,
  }) : _firestore = firestore ?? FirebaseFirestore.instance,
       _offlineStorage = offlineStorage ?? OfflineStorageService(),
       _firebaseStorage = firebaseStorage ?? FirebaseStorageService(),
       _zipExtraction = zipExtraction ?? ZipExtractionService(),
       _assetStoryService = assetStoryService ?? AssetStoryService(),
       _logger = Logger();

  /// Fetches the list of story metadata for the story library from multiple sources
  Future<List<StoryMetadataModel>> fetchStoryMetadataList() async {
    List<StoryMetadataModel> stories = [];

    _logger.i('[StoryRepository] Fetching story metadata from multiple sources');

    // Try to fetch from Firebase first
    try {
      final querySnapshot = await _firestore
          .collection('stories')
          .get();

      for (var doc in querySnapshot.docs) {
        try {
          final story = StoryMetadataModel.fromJson({
            'id': doc.id,
            'dataSource': 'firestore',
            ...doc.data(),
          });
          stories.add(story);
        } catch (e) {
          _logger.w('[StoryRepository] Failed to parse Firestore story: ${doc.id} - $e');
        }
      }
      _logger.i('[StoryRepository] Loaded ${stories.length} stories from Firestore');
    } catch (e) {
      _logger.w('[StoryRepository] Failed to fetch from Firestore: $e');
    }

    // Add asset-based stories
    try {
      final assetStoryIds = await _assetStoryService.getAvailableStoryIds();
      final existingIds = stories.map((s) => s.id).toSet();

      for (final storyId in assetStoryIds) {
        if (!existingIds.contains(storyId)) {
          try {
            final assetStory = await _assetStoryService.loadStoryFromAssets(storyId);
            if (assetStory != null) {
              final metadata = _createMetadataFromStory(assetStory, storyId);
              stories.add(metadata);
            }
          } catch (e) {
            _logger.w('[StoryRepository] Failed to load asset story metadata: $storyId - $e');
          }
        }
      }
      _logger.i('[StoryRepository] Added ${assetStoryIds.length} asset-based stories');
    } catch (e) {
      _logger.w('[StoryRepository] Failed to load asset stories: $e');
    }

    // Always add mock stories as fallback if they're not already in the list
    final mockStories = _getMockStoryMetadata();
    final existingIds = stories.map((s) => s.id).toSet();

    for (final mockStory in mockStories) {
      if (!existingIds.contains(mockStory.id)) {
        stories.add(mockStory);
      }
    }

    // Unlock all stories for testing purposes
    final unlockedStories = stories.map((story) {
      if (story.isLocked) {
        return story.copyWith(isLocked: false);
      }
      return story;
    }).toList();

    // Sort by ID to ensure consistent ordering
    unlockedStories.sort((a, b) => a.id.compareTo(b.id));

    _logger.i('[StoryRepository] Total stories available: ${unlockedStories.length}');
    return unlockedStories;
  }

  /// Fetches a complete story by ID using priority-based loading
  /// Priority: Local Storage → Assets → Firebase Storage
  Future<StoryModel?> fetchStoryById(String storyId, {String dataSource = 'auto', String version = '1.0.0'}) async {
    _logger.i('[StoryRepository] Fetching story: $storyId with priority loading');

    // Priority 1: Check local storage (extracted from ZIP)
    try {
      final isLocallyAvailable = await _zipExtraction.isStoryDownloaded(storyId);
      if (isLocallyAvailable) {
        _logger.i('[StoryRepository] Loading story from local storage: $storyId');
        return await _loadStoryFromLocalStorage(storyId);
      }
    } catch (e) {
      _logger.w('[StoryRepository] Failed to check local storage for $storyId: $e');
    }

    // Priority 2: Check offline storage (legacy system)
    try {
      final isDownloaded = await _offlineStorage.isStoryDownloaded(storyId, version);
      if (isDownloaded) {
        final localStory = await _offlineStorage.getLocalStory(storyId, version: version);
        if (localStory != null) {
          _logger.i('[StoryRepository] Loading story from offline storage: $storyId');
          return localStory;
        }
      }
    } catch (e) {
      _logger.w('[StoryRepository] Failed to check offline storage for $storyId: $e');
    }

    // Priority 3: Check bundled assets using AssetStoryService
    try {
      _logger.i('[StoryRepository] Attempting to load story from assets: $storyId');
      final assetStory = await _assetStoryService.loadStoryFromAssets(storyId);
      if (assetStory != null) {
        _logger.i('[StoryRepository] Successfully loaded story from assets: $storyId');
        return assetStory;
      }
    } catch (e) {
      _logger.w('[StoryRepository] Failed to load story from assets: $storyId - $e');
    }

    // Priority 4: Check Firebase Storage for ZIP file
    try {
      _logger.i('[StoryRepository] Checking Firebase Storage for story: $storyId');
      final storyExists = await _firebaseStorage.storyExists(storyId);
      if (storyExists) {
        _logger.i('[StoryRepository] Story found in Firebase Storage, but not downloaded: $storyId');
        throw StoryLoadException(
          'Story available for download but not yet downloaded',
          technicalDetails: 'Story $storyId exists in Firebase Storage but needs to be downloaded first',
        );
      }
    } catch (e) {
      if (e is StoryLoadException) rethrow;
      _logger.w('[StoryRepository] Failed to check Firebase Storage for $storyId: $e');
    }

    // Priority 5: Fallback to Firestore (legacy)
    if (dataSource == 'firestore') {
      try {
        _logger.i('[StoryRepository] Attempting to load story from Firestore: $storyId');
        final docSnapshot = await _firestore
            .collection('stories')
            .doc(storyId)
            .get();

        if (docSnapshot.exists) {
          return StoryModel.fromJson({
            'id': docSnapshot.id,
            ...docSnapshot.data()!,
          });
        }
      } catch (e) {
        _logger.e('[StoryRepository] Failed to load story from Firestore: $storyId - $e');
      }
    }

    // Story not found anywhere
    _logger.e('[StoryRepository] Story not found in any source: $storyId');
    throw StoryLoadException(
      'Story not found',
      technicalDetails: 'Story $storyId not found in local storage, assets, Firebase Storage, or Firestore',
    );
  }

  /// Creates story metadata from a loaded story model
  StoryMetadataModel _createMetadataFromStory(StoryModel story, String storyId) {
    return StoryMetadataModel(
      id: storyId,
      title: {'en-US': story.title},
      coverImageUrl: story.coverImageUrl ?? 'assets/images/story_covers/placeholder_cover.jpg',
      loglineShort: story.logline.isNotEmpty ? story.logline : {'en-US': 'An interactive story'},
      targetMoralValue: story.targetMoralValue,
      version: story.version ?? '1.0.0',
      isNew: false,
      hasUpdate: false,
      isLocked: false,
      supportedLanguages: story.supportedLanguages,
      defaultLanguage: story.defaultLanguage,
      isFree: story.isFree,
      estimatedDurationMinutes: story.estimatedDurationMinutes,
      published: story.published,
      targetAgeSubSegment: story.targetAgeSubSegment,
      initialSceneId: story.initialSceneId ?? (story.scenes.isNotEmpty ? story.scenes.first.sceneId : 'scene_1'),
      dataSource: 'asset',
      estimatedSizeMb: 5, // Default size for asset stories
      hasProgress: false,
      rewards: story.rewards,
    );
  }

  /// Load story from local storage (extracted ZIP files)
  Future<StoryModel?> _loadStoryFromLocalStorage(String storyId) async {
    try {
      _logger.i('[StoryRepository] Loading story from local storage: $storyId');

      final storyPath = await _zipExtraction.getStoryPath(storyId);
      final storyFile = File('$storyPath/story.json');

      if (!await storyFile.exists()) {
        throw StoryLoadException('Story file not found in local storage');
      }

      final jsonString = await storyFile.readAsString();
      final jsonData = jsonDecode(jsonString) as Map<String, dynamic>;
      final transformedData = _transformStoryData(jsonData);

      _logger.i('[StoryRepository] Successfully loaded story from local storage: $storyId');
      return StoryModel.fromJson(transformedData);

    } catch (e) {
      _logger.e('[StoryRepository] Failed to load story from local storage: $storyId - $e');
      throw StoryLoadException(
        'Failed to load story from local storage',
        technicalDetails: e.toString(),
      );
    }
  }



  /// Checks if a story has an update available
  Future<bool> hasStoryUpdate(String storyId, String currentVersion) async {
    try {
      final docSnapshot = await _firestore
          .collection('stories')
          .doc(storyId)
          .get();

      if (!docSnapshot.exists) return false;
      final latestVersion = docSnapshot.data()!['version'] as String;
      return latestVersion != currentVersion;
    } catch (e) {
      return false;
    }
  }

  /// Transform story data to ensure compatibility between different JSON formats
  Map<String, dynamic> _transformStoryData(Map<String, dynamic> jsonData) {
    final transformed = Map<String, dynamic>.from(jsonData);

    if (transformed['scenes'] is List) {
      final scenes = transformed['scenes'] as List;
      for (int i = 0; i < scenes.length; i++) {
        final scene = scenes[i] as Map<String, dynamic>;
        if (scene['textSegmentsForTTS'] != null) {
          scene['narratorSegments'] = scene['textSegmentsForTTS'];
          scene.remove('textSegmentsForTTS');
        }
        if (scene['backgroundImageUrl'] != null) {
          scene['backgroundImage'] = scene['backgroundImageUrl'];
          scene.remove('backgroundImageUrl');
        }
        if (scene['sceneType'] == null) {
          scene['sceneType'] = (scene['isChoicePoint'] == true || scene['choices'] != null)
              ? 'choice_point'
              : 'narration_illustration';
        }
        scenes[i] = scene;
      }
    }
    if (transformed['workingTitle'] != null && transformed['title'] == null) {
      transformed['title'] = transformed['workingTitle'];
    }
    return transformed;
  }

  /// Returns mock story metadata for development/testing
  List<StoryMetadataModel> _getMockStoryMetadata() {
    return [
      const StoryMetadataModel(
        id: 'simple_test_story',
        title: {'en-US': 'Simple Test Story'},
        coverImageUrl: 'assets/images/story_covers/simple_test_cover.png',
        loglineShort: {'en-US': 'A simple story for testing.'},
        targetMoralValue: 'Kindness',
        isFree: true,
        targetAgeSubSegment: '3-5',
        initialSceneId: 'scene_01',
      ),
      const StoryMetadataModel(
        id: 'pip_pantry_puzzle_v1',
        title: {'en-US': 'Pip and the Pantry Puzzle'},
        coverImageUrl: 'assets/images/story_covers/pip_pantry_puzzle_cover.png',
        loglineShort: {'en-US': 'Help Pip solve the mystery of the missing cookies!'},
        targetMoralValue: 'Honesty',
        isFree: true,
        targetAgeSubSegment: '4-6',
        initialSceneId: 'scene_pip_01_intro',
      ),
      const StoryMetadataModel(
        id: 'lila_moonpetal_wish',
        title: {'en-US': 'Lila and the Moonpetal Wish'},
        coverImageUrl: 'assets/images/story_covers/lila_moonpetal_wish_cover.png',
        loglineShort: {'en-US': 'Join Lila on a magical journey to help a friend.'},
        targetMoralValue: 'Empathy',
        isFree: true,
        targetAgeSubSegment: '5-7',
        initialSceneId: 'scene_lila_01_intro',
      ),
      const StoryMetadataModel(
        id: 'finley_flying_machine',
        title: {'en-US': "Finley's Flying Machine"},
        coverImageUrl: 'assets/images/story_covers/finley_flying_machine_cover.png',
        loglineShort: {'en-US': 'Watch Finley never give up on their dream to fly.'},
        targetMoralValue: 'Perseverance',
        isLocked: true, // This will be unlocked by the fetch method
        isFree: false,
        targetAgeSubSegment: '6-7',
        initialSceneId: 'scene_finley_01_intro',
      ),
      const StoryMetadataModel(
        id: 'SHIVA_EMPATHY_JOURNEY_01',
        title: {"en-US": "Shiva and the New Student's First Day"},
        coverImageUrl: 'gs://choice-tales.firebasestorage.app/stories/SHIVA_EMPATHY_JOURNEY_01/covers/cover.jpg',
        loglineShort: {'en-US': "Shiva learns to understand and care about others' feelings."},
        targetMoralValue: 'Empathy',
        dataSource: 'asset',
        isFree: true,
        targetAgeSubSegment: '6-8',
        initialSceneId: 'SHIVA_S01_SCHOOL_MORNING',
      ),
    ];
  }

  /// Downloads and extracts a story from Firebase Storage
  ///
  /// [storyId] - The ID of the story to download
  /// [onProgress] - Optional callback for download progress (0.0 to 1.0)
  /// [onStatusUpdate] - Optional callback for status updates
  ///
  /// Returns true if the story was successfully downloaded and extracted
  Future<bool> downloadStoryFromFirebase(
    String storyId, {
    Function(double progress)? onProgress,
    Function(String status)? onStatusUpdate,
  }) async {
    try {
      _logger.i('[StoryRepository] Starting download for story: $storyId');
      onStatusUpdate?.call('Checking if story exists...');

      // Check if story exists in Firebase Storage
      final storyExists = await _firebaseStorage.storyExists(storyId);
      if (!storyExists) {
        throw StoryLoadException('Story not found in Firebase Storage');
      }

      // Check if already downloaded
      final isAlreadyDownloaded = await _zipExtraction.isStoryDownloaded(storyId);
      if (isAlreadyDownloaded) {
        _logger.i('[StoryRepository] Story already downloaded: $storyId');
        onStatusUpdate?.call('Story already downloaded');
        onProgress?.call(1.0);
        return true;
      }

      // Download the ZIP file
      onStatusUpdate?.call('Downloading story...');
      final zipPath = await _firebaseStorage.downloadStoryZip(
        storyId,
        onProgress: (downloadProgress) {
          // Download takes 70% of total progress
          onProgress?.call(downloadProgress * 0.7);
        },
      );

      // Extract the ZIP file
      onStatusUpdate?.call('Extracting story...');
      final extractedPath = await _zipExtraction.extractStoryZip(
        zipPath,
        storyId,
        onProgress: (extractProgress) {
          // Extraction takes 30% of total progress
          onProgress?.call(0.7 + (extractProgress * 0.3));
        },
      );

      // Validate the extracted story
      final isValid = await _zipExtraction.validateStoryStructure(extractedPath);
      if (!isValid) {
        throw StoryLoadException('Downloaded story has invalid structure');
      }

      onStatusUpdate?.call('Download complete');
      onProgress?.call(1.0);
      _logger.i('[StoryRepository] Successfully downloaded and extracted story: $storyId');
      return true;

    } catch (e) {
      _logger.e('[StoryRepository] Failed to download story $storyId: $e');
      onStatusUpdate?.call('Download failed');

      if (e is StoryLoadException) {
        rethrow;
      }
      throw StoryLoadException(
        'Failed to download story from Firebase Storage',
        technicalDetails: e.toString(),
      );
    }
  }

  /// Checks if a story is available for download from Firebase Storage
  ///
  /// [storyId] - The ID of the story to check
  ///
  /// Returns true if the story can be downloaded
  Future<bool> isStoryAvailableForDownload(String storyId) async {
    try {
      return await _firebaseStorage.storyExists(storyId);
    } catch (e) {
      _logger.w('[StoryRepository] Failed to check story availability: $storyId - $e');
      return false;
    }
  }

  /// Gets the local storage status for a story
  ///
  /// [storyId] - The ID of the story to check
  ///
  /// Returns a map with storage information
  Future<Map<String, dynamic>> getStoryStorageInfo(String storyId) async {
    try {
      final isLocallyAvailable = await _zipExtraction.isStoryDownloaded(storyId);
      final isAvailableForDownload = await isStoryAvailableForDownload(storyId);
      final localSize = isLocallyAvailable ? await _zipExtraction.getLocalStorySize(storyId) : 0;

      return {
        'isLocallyAvailable': isLocallyAvailable,
        'isAvailableForDownload': isAvailableForDownload,
        'localSizeBytes': localSize,
        'localSizeMB': (localSize / (1024 * 1024)).round(),
      };
    } catch (e) {
      _logger.e('[StoryRepository] Failed to get storage info for $storyId: $e');
      return {
        'isLocallyAvailable': false,
        'isAvailableForDownload': false,
        'localSizeBytes': 0,
        'localSizeMB': 0,
      };
    }
  }

  /// Deletes a locally stored story
  ///
  /// [storyId] - The ID of the story to delete
  ///
  /// Returns true if the story was successfully deleted
  Future<bool> deleteLocalStory(String storyId) async {
    try {
      _logger.i('[StoryRepository] Deleting local story: $storyId');
      final success = await _zipExtraction.deleteLocalStory(storyId);

      if (success) {
        _logger.i('[StoryRepository] Successfully deleted local story: $storyId');
      } else {
        _logger.w('[StoryRepository] Story was not found locally: $storyId');
      }

      return success;
    } catch (e) {
      _logger.e('[StoryRepository] Failed to delete local story $storyId: $e');
      return false;
    }
  }
}
