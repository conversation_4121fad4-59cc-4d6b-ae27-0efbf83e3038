import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/services.dart';
import 'package:choice_once_upon_a_time/core/services/enhanced_story_service.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart';

void main() {
  group('EnhancedStoryService Tests', () {
    late EnhancedStoryService service;

    setUp(() {
      service = EnhancedStoryService();
    });

    tearDown(() {
      service.clearCache();
    });

    group('Story Scanning', () {
      test('should scan available stories from asset manifest', () async {
        // Mock asset manifest
        const mockManifest = '''
        {
          "assets/stories/story001/story001.json": ["assets/stories/story001/story001.json"],
          "assets/stories/story002/story002.json": ["assets/stories/story002/story002.json"],
          "assets/stories/invalid/other.json": ["assets/stories/invalid/other.json"],
          "assets/images/cover.jpg": ["assets/images/cover.jpg"]
        }
        ''';

        // Set up mock asset bundle
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString' &&
                methodCall.arguments == 'AssetManifest.json') {
              return mockManifest;
            }
            return null;
          },
        );

        final storyIds = await service.scanAvailableStories();

        expect(storyIds, contains('story001'));
        expect(storyIds, contains('story002'));
        expect(storyIds, isNot(contains('invalid')));
        expect(storyIds.length, equals(2));
      });

      test('should handle empty asset manifest', () async {
        const mockManifest = '{}';

        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString' &&
                methodCall.arguments == 'AssetManifest.json') {
              return mockManifest;
            }
            return null;
          },
        );

        final storyIds = await service.scanAvailableStories();

        expect(storyIds, isEmpty);
      });

      test('should handle asset manifest loading error', () async {
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            throw PlatformException(code: 'ASSET_NOT_FOUND', message: 'Asset not found');
          },
        );

        final storyIds = await service.scanAvailableStories();

        expect(storyIds, isEmpty);
      });
    });

    group('Story Loading', () {
      test('should load valid story from assets', () async {
        const mockStoryJson = '''
        {
          "story_id": "test_story",
          "age_group": "3-5",
          "difficulty": "easy",
          "title": "Test Story",
          "moral": "kindness",
          "cover_image": "cover.jpg",
          "setup": {
            "setting": "A magical forest",
            "tone": "cheerful",
            "context": "A story about friendship"
          },
          "narrator_profile": {
            "name": "Friendly Narrator",
            "voice": {
              "pitch": 1.0,
              "rate": 0.5,
              "volume": 1.0
            }
          },
          "characters": [
            {
              "name": "Alice",
              "description": "A kind girl",
              "role": "protagonist",
              "voice": {
                "pitch": 1.2,
                "rate": 0.6,
                "volume": 0.9
              }
            }
          ],
          "scenes": [
            {
              "id": "scene1",
              "text": "Once upon a time...",
              "speaker": "Narrator",
              "emotion": "calm",
              "image": "scene1.jpg",
              "pause_duration": 1000
            }
          ],
          "post_story": {
            "discussion": {
              "text": "What did you learn?",
              "emotion": "curious"
            }
          }
        }
        ''';

        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString' &&
                methodCall.arguments == 'assets/stories/test_story/test_story.json') {
              return mockStoryJson;
            }
            return null;
          },
        );

        final story = await service.loadStory('test_story');

        expect(story, isNotNull);
        expect(story!.storyId, equals('test_story'));
        expect(story.title, equals('Test Story'));
        expect(story.characters.length, equals(1));
        expect(story.scenes.length, equals(1));
      });

      test('should return null for non-existent story', () async {
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            throw PlatformException(code: 'ASSET_NOT_FOUND', message: 'Asset not found');
          },
        );

        final story = await service.loadStory('non_existent');

        expect(story, isNull);
      });

      test('should cache loaded stories', () async {
        const mockStoryJson = '''
        {
          "story_id": "cached_story",
          "age_group": "3-5",
          "difficulty": "easy",
          "title": "Cached Story",
          "moral": "kindness",
          "cover_image": "cover.jpg",
          "setup": {
            "setting": "A magical forest",
            "tone": "cheerful",
            "context": "A story about friendship"
          },
          "narrator_profile": {
            "name": "Friendly Narrator",
            "voice": {
              "pitch": 1.0,
              "rate": 0.5,
              "volume": 1.0
            }
          },
          "characters": [],
          "scenes": [
            {
              "id": "scene1",
              "text": "Once upon a time...",
              "speaker": "Narrator",
              "emotion": "calm",
              "image": "scene1.jpg",
              "pause_duration": 1000
            }
          ],
          "post_story": {}
        }
        ''';

        int loadCount = 0;
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString' &&
                methodCall.arguments == 'assets/stories/cached_story/cached_story.json') {
              loadCount++;
              return mockStoryJson;
            }
            return null;
          },
        );

        // Load story twice
        final story1 = await service.loadStory('cached_story');
        final story2 = await service.loadStory('cached_story');

        expect(story1, isNotNull);
        expect(story2, isNotNull);
        expect(story1!.storyId, equals(story2!.storyId));
        expect(loadCount, equals(1)); // Should only load once due to caching
      });
    });

    group('Story Validation', () {
      test('should validate story structure correctly', () async {
        const validStoryJson = '''
        {
          "story_id": "valid_story",
          "title": "Valid Story",
          "moral": "kindness",
          "setup": {
            "setting": "A magical forest",
            "tone": "cheerful",
            "context": "A story about friendship"
          },
          "narrator_profile": {
            "name": "Friendly Narrator",
            "voice": {
              "pitch": 1.0,
              "rate": 0.5,
              "volume": 1.0
            }
          },
          "characters": [],
          "scenes": [
            {
              "id": "scene1",
              "text": "Once upon a time...",
              "speaker": "Narrator",
              "emotion": "calm",
              "image": "scene1.jpg",
              "pause_duration": 1000
            }
          ],
          "post_story": {}
        }
        ''';

        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString') {
              return validStoryJson;
            }
            return null;
          },
        );

        final story = await service.loadStory('valid_story');

        expect(story, isNotNull);
      });

      test('should reject story with mismatched ID', () async {
        const invalidStoryJson = '''
        {
          "story_id": "different_id",
          "title": "Test Story",
          "moral": "kindness",
          "setup": {},
          "narrator_profile": {
            "name": "Narrator",
            "voice": {"pitch": 1.0, "rate": 0.5, "volume": 1.0}
          },
          "characters": [],
          "scenes": [{"id": "scene1", "text": "text", "speaker": "Narrator", "emotion": "calm", "image": "img.jpg", "pause_duration": 1000}],
          "post_story": {}
        }
        ''';

        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            return invalidStoryJson;
          },
        );

        final story = await service.loadStory('expected_id');

        expect(story, isNull);
      });
    });

    group('Metadata Creation', () {
      test('should create metadata from story', () async {
        const mockStoryJson = '''
        {
          "story_id": "metadata_test",
          "age_group": "5-8",
          "difficulty": "medium",
          "title": "Metadata Test Story",
          "moral": "courage",
          "cover_image": "cover.jpg",
          "setup": {
            "setting": "A brave adventure",
            "tone": "exciting",
            "context": "A story about being brave"
          },
          "narrator_profile": {
            "name": "Brave Narrator",
            "voice": {
              "pitch": 1.0,
              "rate": 0.5,
              "volume": 1.0
            }
          },
          "characters": [],
          "scenes": [
            {
              "id": "scene1",
              "text": "Be brave!",
              "speaker": "Narrator",
              "emotion": "encouraging",
              "image": "scene1.jpg",
              "pause_duration": 1000
            },
            {
              "id": "scene2",
              "text": "You did it!",
              "speaker": "Narrator",
              "emotion": "proud",
              "image": "scene2.jpg",
              "pause_duration": 1000
            }
          ],
          "post_story": {}
        }
        ''';

        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString') {
              if (methodCall.arguments == 'AssetManifest.json') {
                return '{"assets/stories/metadata_test/metadata_test.json": []}';
              }
              return mockStoryJson;
            }
            return null;
          },
        );

        final metadataList = await service.getAllStoryMetadata();

        expect(metadataList.length, equals(1));
        final metadata = metadataList.first;
        expect(metadata.id, equals('metadata_test'));
        expect(metadata.title['en-US'], equals('Metadata Test Story'));
        expect(metadata.targetMoralValue, equals('courage'));
        expect(metadata.targetAgeSubSegment, equals('5-8'));
      });
    });

    group('Cache Management', () {
      test('should clear cache correctly', () {
        service.clearCache();
        final stats = service.getCacheStats();

        expect(stats['storiesLoaded'], equals(0));
        expect(stats['metadataCached'], equals(0));
        expect(stats['allStoriesCached'], isFalse);
      });

      test('should provide cache statistics', () {
        final stats = service.getCacheStats();

        expect(stats, containsPair('storiesLoaded', isA<int>()));
        expect(stats, containsPair('metadataCached', isA<int>()));
        expect(stats, containsPair('allStoriesCached', isA<bool>()));
      });
    });

    group('Helper Methods', () {
      test('should check if story exists', () async {
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.arguments == 'assets/stories/existing_story/existing_story.json') {
              return '{"story_id": "existing_story", "title": "Test", "moral": "test", "setup": {}, "narrator_profile": {"name": "Test", "voice": {"pitch": 1.0, "rate": 0.5, "volume": 1.0}}, "characters": [], "scenes": [{"id": "scene1", "text": "text", "speaker": "Narrator", "emotion": "calm", "image": "img.jpg", "pause_duration": 1000}], "post_story": {}}';
            }
            throw PlatformException(code: 'ASSET_NOT_FOUND', message: 'Asset not found');
          },
        );

        final exists = await service.storyExists('existing_story');
        final notExists = await service.storyExists('non_existing_story');

        expect(exists, isTrue);
        expect(notExists, isFalse);
      });
    });
  });
}
