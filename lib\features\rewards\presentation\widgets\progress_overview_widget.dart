import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Widget displaying overall progress with animated charts
class ProgressOverviewWidget extends StatefulWidget {
  final Set<String> completedStories;
  final Map<String, List<String>> earnedRewards;
  final Map<String, int> choiceRewards;
  final AnimationController animationController;

  const ProgressOverviewWidget({
    super.key,
    required this.completedStories,
    required this.earnedRewards,
    required this.choiceRewards,
    required this.animationController,
  });

  @override
  State<ProgressOverviewWidget> createState() => _ProgressOverviewWidgetState();
}

class _ProgressOverviewWidgetState extends State<ProgressOverviewWidget>
    with SingleTickerProviderStateMixin {
  late final AnimationController _progressController;
  late final Animation<double> _progressAnimation;
  late final Animation<double> _chartAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startProgressAnimation();
  }

  void _initializeAnimations() {
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeOutCubic,
    ));

    _chartAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: const Interval(0.3, 1.0, curve: Curves.elasticOut),
    ));
  }

  Future<void> _startProgressAnimation() async {
    await Future.delayed(const Duration(milliseconds: 500));
    if (mounted) {
      _progressController.forward();
    }
  }

  @override
  void dispose() {
    _progressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    return Card(
      elevation: 6,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        padding: EdgeInsets.all(isSmallScreen ? 16.0 : 24.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              theme.colorScheme.primaryContainer.withOpacity(0.3),
              theme.colorScheme.surface,
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.trending_up,
                  color: theme.colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Your Progress',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Progress charts
            if (isSmallScreen)
              Column(
                children: [
                  _buildCircularProgress(theme),
                  const SizedBox(height: 24),
                  _buildProgressBars(theme),
                ],
              )
            else
              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: _buildCircularProgress(theme),
                  ),
                  const SizedBox(width: 24),
                  Expanded(
                    flex: 3,
                    child: _buildProgressBars(theme),
                  ),
                ],
              ),

            const SizedBox(height: 24),

            // Achievement summary
            _buildAchievementSummary(theme),
          ],
        ),
      ),
    );
  }

  Widget _buildCircularProgress(ThemeData theme) {
    final totalStories = 5; // Mock total stories available
    final completedCount = widget.completedStories.length;
    final completionPercentage = completedCount / totalStories;

    return AnimatedBuilder(
      animation: _chartAnimation,
      builder: (context, child) {
        return Container(
          height: 150,
          child: Stack(
            alignment: Alignment.center,
            children: [
              // Background circle
              SizedBox(
                width: 120,
                height: 120,
                child: CircularProgressIndicator(
                  value: 1.0,
                  strokeWidth: 8,
                  backgroundColor: theme.colorScheme.outline.withOpacity(0.2),
                  valueColor: AlwaysStoppedAnimation<Color>(
                    theme.colorScheme.outline.withOpacity(0.1),
                  ),
                ),
              ),
              
              // Progress circle
              SizedBox(
                width: 120,
                height: 120,
                child: CircularProgressIndicator(
                  value: completionPercentage * _chartAnimation.value,
                  strokeWidth: 8,
                  backgroundColor: Colors.transparent,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    theme.colorScheme.primary,
                  ),
                ),
              ),
              
              // Center text
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    '${(completionPercentage * 100 * _chartAnimation.value).round()}%',
                    style: theme.textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                  Text(
                    'Complete',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildProgressBars(ThemeData theme) {
    final totalRewards = widget.earnedRewards.values.fold(0, (sum, rewards) => sum + rewards.length);
    final totalChoices = widget.choiceRewards.values.fold(0, (sum, count) => sum + count);

    return Column(
      children: [
        _buildProgressBar(
          label: 'Stories Completed',
          value: widget.completedStories.length,
          maxValue: 5,
          color: theme.colorScheme.primary,
          theme: theme,
        ),
        
        const SizedBox(height: 16),
        
        _buildProgressBar(
          label: 'Rewards Earned',
          value: totalRewards,
          maxValue: 15,
          color: Colors.amber,
          theme: theme,
        ),
        
        const SizedBox(height: 16),
        
        _buildProgressBar(
          label: 'Good Choices Made',
          value: totalChoices,
          maxValue: 10,
          color: Colors.green,
          theme: theme,
        ),
      ],
    );
  }

  Widget _buildProgressBar({
    required String label,
    required int value,
    required int maxValue,
    required Color color,
    required ThemeData theme,
  }) {
    final progress = value / maxValue;

    return AnimatedBuilder(
      animation: _progressAnimation,
      builder: (context, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  label,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  '$value/$maxValue',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 8),
            
            Container(
              height: 8,
              decoration: BoxDecoration(
                color: color.withOpacity(0.2),
                borderRadius: BorderRadius.circular(4),
              ),
              child: FractionallySizedBox(
                alignment: Alignment.centerLeft,
                widthFactor: progress * _progressAnimation.value,
                child: Container(
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildAchievementSummary(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.secondaryContainer.withOpacity(0.5),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Icon(
            Icons.emoji_events,
            color: Colors.amber,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Keep Going!',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSecondaryContainer,
                  ),
                ),
                Text(
                  'You\'re doing great! Complete more stories to unlock new rewards.',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSecondaryContainer,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
