import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import 'package:choice_once_upon_a_time/models/story_metadata_model.dart';
import 'package:choice_once_upon_a_time/models/story_model.dart';
import 'package:choice_once_upon_a_time/models/rewards_model.dart';

/// Service for managing asset-based story loading and validation
class AssetStoryService {
  static final AssetStoryService _instance = AssetStoryService._internal();
  factory AssetStoryService() => _instance;
  AssetStoryService._internal();

  final Logger _logger = Logger();
  
  // Cache for loaded stories to avoid repeated asset loading
  final Map<String, StoryModel> _storyCache = {};
  final Map<String, dynamic> _storyListCache = {};

  /// Gets the list of available story IDs from the assets directory
  ///
  /// Returns a list of story IDs that have valid story.json files
  Future<List<String>> getAvailableStoryIds() async {
    try {
      _logger.i('[AssetStoryService] Loading available story IDs from assets');

      // Check cache first
      if (_storyListCache.containsKey('available_stories')) {
        return (_storyListCache['available_stories'] as List<String>);
      }

      // Load the asset manifest to find story directories
      final manifestContent = await rootBundle.loadString('AssetManifest.json');
      final Map<String, dynamic> manifest = jsonDecode(manifestContent);

      final storyIds = <String>[];
      final storyPaths = manifest.keys
          .where((String key) => key.startsWith('assets/stories/') && key.endsWith('/story.json'))
          .toList();

      for (final path in storyPaths) {
        try {
          // Load the story.json to get the actual story ID
          final jsonString = await rootBundle.loadString(path);
          final jsonData = jsonDecode(jsonString) as Map<String, dynamic>;

          // Use the ID from the JSON file, not the folder name
          final storyId = jsonData['id'] as String?;
          if (storyId != null && storyId.isNotEmpty) {
            storyIds.add(storyId);

            // Cache the folder path for this story ID
            final pathParts = path.split('/');
            if (pathParts.length >= 3) {
              final folderName = pathParts[2];
              _storyListCache['folder_$storyId'] = folderName;
            }
          }
        } catch (e) {
          _logger.w('[AssetStoryService] Failed to load story from path $path: $e');
        }
      }

      // Cache the result
      _storyListCache['available_stories'] = storyIds;

      _logger.i('[AssetStoryService] Found ${storyIds.length} stories in assets: $storyIds');
      return storyIds;

    } catch (e) {
      _logger.e('[AssetStoryService] Failed to load available story IDs: $e');
      return [];
    }
  }

  /// Checks if a story exists in the assets directory
  ///
  /// [storyId] - The ID of the story to check
  ///
  /// Returns true if the story has a valid story.json file
  Future<bool> storyExistsInAssets(String storyId) async {
    try {
      // Get the folder name for this story ID
      String folderName = storyId;
      if (_storyListCache.containsKey('folder_$storyId')) {
        folderName = _storyListCache['folder_$storyId'] as String;
      } else {
        // If not cached, try to find it by loading available stories first
        final availableStories = await getAvailableStoryIds();
        if (!availableStories.contains(storyId)) {
          return false;
        }
        folderName = _storyListCache['folder_$storyId'] as String;
      }

      final storyPath = 'assets/stories/$folderName/story.json';
      await rootBundle.loadString(storyPath);
      return true;
    } catch (e) {
      _logger.d('[AssetStoryService] Story not found in assets: $storyId');
      return false;
    }
  }

  /// Loads a story from the assets directory
  ///
  /// [storyId] - The ID of the story to load
  ///
  /// Returns the loaded StoryModel or null if not found
  Future<StoryModel?> loadStoryFromAssets(String storyId) async {
    try {
      _logger.i('[AssetStoryService] Loading story from assets: $storyId');

      // Check cache first
      if (_storyCache.containsKey(storyId)) {
        _logger.d('[AssetStoryService] Returning cached story: $storyId');
        return _storyCache[storyId];
      }

      // Get the folder name for this story ID
      String folderName = storyId;
      if (_storyListCache.containsKey('folder_$storyId')) {
        folderName = _storyListCache['folder_$storyId'] as String;
      } else {
        // If not cached, try to find it by loading available stories first
        await getAvailableStoryIds();
        if (_storyListCache.containsKey('folder_$storyId')) {
          folderName = _storyListCache['folder_$storyId'] as String;
        }
      }

      // Load the story.json file using the folder name
      final storyPath = 'assets/stories/$folderName/story.json';
      final jsonString = await rootBundle.loadString(storyPath);
      final jsonData = jsonDecode(jsonString) as Map<String, dynamic>;

      // Transform the data to match our StoryModel format
      final transformedData = _transformAssetStoryData(jsonData, storyId);
      final story = StoryModel.fromJson(transformedData);

      // Resolve asset paths
      final storyWithResolvedPaths = _resolveAssetPaths(story, folderName);

      // Cache the result
      _storyCache[storyId] = storyWithResolvedPaths;

      _logger.i('[AssetStoryService] Successfully loaded story from assets: $storyId');
      return storyWithResolvedPaths;

    } catch (e) {
      _logger.e('[AssetStoryService] Failed to load story from assets: $storyId - $e');
      return null;
    }
  }

  /// Validates the asset structure for a story
  /// 
  /// [storyId] - The ID of the story to validate
  /// 
  /// Returns a map with validation results
  Future<Map<String, dynamic>> validateStoryAssets(String storyId) async {
    final validation = {
      'storyId': storyId,
      'hasStoryJson': false,
      'hasImagesFolder': false,
      'hasAudioFolder': false,
      'missingAssets': <String>[],
      'isValid': false,
    };

    try {
      // Get the folder name for this story ID
      String folderName = storyId;
      if (_storyListCache.containsKey('folder_$storyId')) {
        folderName = _storyListCache['folder_$storyId'] as String;
      } else {
        // If not cached, try to find it by loading available stories first
        await getAvailableStoryIds();
        if (_storyListCache.containsKey('folder_$storyId')) {
          folderName = _storyListCache['folder_$storyId'] as String;
        }
      }

      // Check story.json
      final storyPath = 'assets/stories/$folderName/story.json';
      try {
        await rootBundle.loadString(storyPath);
        validation['hasStoryJson'] = true;
      } catch (e) {
        (validation['missingAssets'] as List<String>).add('story.json');
      }

      // Check for images and audio folders by looking at manifest
      final manifestContent = await rootBundle.loadString('AssetManifest.json');
      final Map<String, dynamic> manifest = jsonDecode(manifestContent);

      final hasImages = manifest.keys.any((key) => key.startsWith('assets/stories/$folderName/images/'));
      final hasAudio = manifest.keys.any((key) => key.startsWith('assets/stories/$folderName/audio/'));

      validation['hasImagesFolder'] = hasImages;
      validation['hasAudioFolder'] = hasAudio;

      if (!hasImages) (validation['missingAssets'] as List<String>).add('images folder');
      if (!hasAudio) (validation['missingAssets'] as List<String>).add('audio folder');

      // Story is valid if it has at least story.json
      validation['isValid'] = validation['hasStoryJson'] as bool;

      _logger.d('[AssetStoryService] Validation for $storyId: $validation');
      return validation;

    } catch (e) {
      _logger.e('[AssetStoryService] Failed to validate story assets: $storyId - $e');
      (validation['missingAssets'] as List<String>).add('validation error: $e');
      return validation;
    }
  }

  /// Transforms asset story data to match our StoryModel format
  Map<String, dynamic> _transformAssetStoryData(Map<String, dynamic> jsonData, String storyId) {
    // Handle different story formats
    final transformed = <String, dynamic>{};

    // Basic story information
    transformed['storyId'] = jsonData['id']?.toString() ?? storyId;
    transformed['workingTitle'] = jsonData['title']?.toString() ?? jsonData['story_title']?.toString() ?? 'Untitled Story';
    transformed['targetCoreMoralValue'] = _extractMoralValue(jsonData);
    transformed['targetAgeSubSegment'] = '5-8'; // Default age range
    transformed['version'] = '1.0.0';
    transformed['narratorPersonaGuidance'] = 'warm, gentle';
    transformed['supportedLanguages'] = ['en-US'];
    transformed['defaultLanguage'] = 'en-US';
    transformed['estimatedDurationMinutes'] = 10;
    transformed['isFree'] = true;
    transformed['published'] = true;
    transformed['logline'] = {'en-US': jsonData['description']?.toString() ?? 'An interactive story'};

    // Handle rewards if present
    if (jsonData.containsKey('rewards')) {
      transformed['rewards'] = jsonData['rewards'];
    }

    // Transform scenes
    transformed['scenes'] = _transformScenes(jsonData['scenes'] ?? [], storyId);

    // Set initial scene
    if (jsonData.containsKey('start') && jsonData['start'] != null) {
      transformed['initialSceneId'] = jsonData['start'].toString();
    } else if (transformed['scenes'] is List && (transformed['scenes'] as List).isNotEmpty) {
      transformed['initialSceneId'] = (transformed['scenes'] as List)[0]['sceneId'];
    }

    return transformed;
  }

  /// Extracts moral value from various story formats
  String _extractMoralValue(Map<String, dynamic> jsonData) {
    if (jsonData.containsKey('targetMoralValue')) {
      return jsonData['targetMoralValue'];
    }
    if (jsonData.containsKey('moral')) {
      return jsonData['moral'];
    }
    if (jsonData.containsKey('moral_values') && jsonData['moral_values'] is List) {
      final moralValues = jsonData['moral_values'] as List;
      return moralValues.isNotEmpty ? moralValues.first.toString() : 'Life Lessons';
    }
    return 'Life Lessons';
  }

  /// Transforms scenes from asset format to our format
  List<Map<String, dynamic>> _transformScenes(List<dynamic> scenes, String storyId) {
    return scenes.map((scene) => _transformScene(scene as Map<String, dynamic>, storyId)).toList();
  }

  /// Transforms a single scene from asset format
  Map<String, dynamic> _transformScene(Map<String, dynamic> scene, String storyId) {
    final transformed = <String, dynamic>{};

    transformed['sceneId'] = scene['id']?.toString() ?? scene['sceneId']?.toString() ?? 'unknown_scene';
    transformed['sceneType'] = _determineSceneType(scene);
    transformed['order'] = scene['order'] ?? 0;

    // Transform narrator segments
    transformed['narratorSegments'] = _createNarratorSegments(scene);

    // Handle choices if present
    if (scene.containsKey('choices') && scene['choices'] is List) {
      final choices = scene['choices'] as List;
      if (choices.isNotEmpty) {
        transformed['isChoicePoint'] = true;
        transformed['choicePointData'] = {
          'choices': choices.map((choice) => _transformChoice(choice as Map<String, dynamic>)).toList(),
        };
      }
    }

    // Set next scene
    if (scene.containsKey('next_scene') && scene['next_scene'] != null) {
      transformed['nextSceneId'] = scene['next_scene'].toString();
    }

    return transformed;
  }

  /// Determines the scene type based on content
  String _determineSceneType(Map<String, dynamic> scene) {
    if (scene.containsKey('choices') && scene['choices'] is List && (scene['choices'] as List).isNotEmpty) {
      return 'choice_point';
    }
    return 'narration_illustration';
  }

  /// Creates narrator segments from scene data
  List<Map<String, dynamic>> _createNarratorSegments(Map<String, dynamic> scene) {
    final segments = <Map<String, dynamic>>[];

    if (scene.containsKey('text') && scene['text'] != null) {
      segments.add({
        'id': '${scene['id'] ?? 'scene'}_narration',
        'text': {'en-US': scene['text'].toString()},
        'emotionCue': _extractEmotionCue(scene),
        'durationEstimateMs': 5000,
      });
    }

    return segments;
  }

  /// Extracts emotion cue from scene data
  String _extractEmotionCue(Map<String, dynamic> scene) {
    if (scene.containsKey('emotional_cue')) {
      final cue = scene['emotional_cue'].toString();
      // Remove brackets if present: [Happy] -> Happy
      return cue.replaceAll(RegExp(r'[\[\]]'), '').toLowerCase();
    }
    return 'neutral';
  }

  /// Transforms a choice from asset format
  Map<String, dynamic> _transformChoice(Map<String, dynamic> choice) {
    return {
      'choiceId': choice['id']?.toString() ?? 'choice_${DateTime.now().millisecondsSinceEpoch}',
      'displayTextKey': choice['text']?.toString() ?? 'Unknown choice',
      'leadsToSceneId': choice['next_scene']?.toString() ?? choice['nextSceneId']?.toString() ?? '',
      'narratorGuidance': {},
    };
  }

  /// Resolves asset paths to be relative to the story folder
  StoryModel _resolveAssetPaths(StoryModel story, String storyId) {
    // For now, return the story as-is since asset path resolution
    // will be handled by the AssetFallbackService
    return story;
  }

  /// Clears the story cache
  void clearCache() {
    _storyCache.clear();
    _storyListCache.clear();
    _logger.i('[AssetStoryService] Cache cleared');
  }

  /// Gets cache statistics
  Map<String, dynamic> getCacheStats() {
    return {
      'cachedStories': _storyCache.length,
      'cachedLists': _storyListCache.length,
      'storyIds': _storyCache.keys.toList(),
    };
  }
}
