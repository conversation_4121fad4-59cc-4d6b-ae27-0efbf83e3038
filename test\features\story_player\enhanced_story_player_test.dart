import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/widgets/welcome_screen_widget.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/widgets/character_profiles_widget.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/widgets/story_scene_widget.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart';
import 'package:choice_once_upon_a_time/core/services/story_narration_service.dart';
import 'package:choice_once_upon_a_time/core/services/story_settings_service.dart';

void main() {
  group('Enhanced Story Player Widget Tests', () {
    late EnhancedStoryModel mockStory;
    late StoryNarrationService mockNarrationService;
    late StorySettingsService mockSettingsService;

    setUp(() {
      // Create mock story
      mockStory = EnhancedStoryModel(
        storyId: 'test_story',
        ageGroup: '3-5',
        difficulty: 'easy',
        title: 'Test Story',
        moral: 'kindness',
        coverImage: 'cover.jpg',
        backgroundMusic: null,
        setup: const StorySetupModel(
          setting: 'A magical forest',
          tone: 'cheerful',
          context: 'A story about friendship',
          briefIntro: 'Welcome to our story!',
        ),
        narratorProfile: const NarratorProfileModel(
          name: 'Friendly Narrator',
          voice: VoiceModel(
            pitch: 1.0,
            rate: 0.5,
            volume: 1.0,
          ),
        ),
        characters: [
          const CharacterModel(
            name: 'Alice',
            description: 'A kind girl',
            role: 'protagonist',
            voice: VoiceModel(
              pitch: 1.2,
              rate: 0.6,
              volume: 0.9,
            ),
          ),
        ],
        scenes: [
          const EnhancedSceneModel(
            id: 'scene1',
            text: 'Once upon a time...',
            speaker: 'Narrator',
            emotion: 'calm',
            image: 'scene1.jpg',
            pauseDuration: 1000,
            next: 'scene2',
          ),
          const EnhancedSceneModel(
            id: 'scene2',
            text: 'What do you choose?',
            speaker: 'Narrator',
            emotion: 'curious',
            image: 'scene2.jpg',
            pauseDuration: 1000,
            choices: [
              ChoiceOptionModel(
                option: 'Go left',
                visual: 'left_arrow.png',
                next: 'scene3',
              ),
              ChoiceOptionModel(
                option: 'Go right',
                visual: 'right_arrow.png',
                next: 'scene4',
              ),
            ],
          ),
        ],
        postStory: const PostStoryModel(
          discussion: ReflectionModel(
            text: 'What did you learn?',
            emotion: 'curious',
          ),
        ),
      );

      mockNarrationService = StoryNarrationService();
      mockSettingsService = StorySettingsService.instance;
    });

    group('WelcomeScreenWidget Tests', () {
      testWidgets('should display story title and cover image', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: WelcomeScreenWidget(
                story: mockStory,
                onContinue: () {},
                narrationService: mockNarrationService,
              ),
            ),
          ),
        );

        expect(find.text('Test Story'), findsOneWidget);
        expect(find.text('kindness'), findsOneWidget);
        expect(find.text('Meet the Characters'), findsOneWidget);
      });

      testWidgets('should display story information chips', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: WelcomeScreenWidget(
                story: mockStory,
                onContinue: () {},
                narrationService: mockNarrationService,
              ),
            ),
          ),
        );

        expect(find.text('3-5'), findsOneWidget);
        expect(find.text('2 scenes'), findsOneWidget);
        expect(find.text('easy'), findsOneWidget);
      });

      testWidgets('should call onContinue when button is tapped', (WidgetTester tester) async {
        bool continueCalled = false;

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: WelcomeScreenWidget(
                story: mockStory,
                onContinue: () => continueCalled = true,
                narrationService: mockNarrationService,
              ),
            ),
          ),
        );

        // Wait for animations to complete
        await tester.pumpAndSettle();

        final continueButton = find.text('Meet the Characters');
        expect(continueButton, findsOneWidget);

        await tester.tap(continueButton);
        await tester.pump();

        expect(continueCalled, isTrue);
      });

      testWidgets('should be responsive on different screen sizes', (WidgetTester tester) async {
        // Test mobile size
        await tester.binding.setSurfaceSize(const Size(400, 800));
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: WelcomeScreenWidget(
                story: mockStory,
                onContinue: () {},
                narrationService: mockNarrationService,
              ),
            ),
          ),
        );

        expect(find.byType(WelcomeScreenWidget), findsOneWidget);

        // Test tablet size
        await tester.binding.setSurfaceSize(const Size(800, 1200));
        await tester.pump();

        expect(find.byType(WelcomeScreenWidget), findsOneWidget);

        // Reset to default size
        await tester.binding.setSurfaceSize(null);
      });
    });

    group('CharacterProfilesWidget Tests', () {
      testWidgets('should display character information', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: CharacterProfilesWidget(
                story: mockStory,
                onContinue: () {},
                narrationService: mockNarrationService,
              ),
            ),
          ),
        );

        expect(find.text('Meet the Characters'), findsOneWidget);
        expect(find.text('Alice'), findsOneWidget);
        expect(find.text('A kind girl'), findsOneWidget);
        expect(find.text('protagonist'), findsOneWidget);
      });

      testWidgets('should show page indicators', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: CharacterProfilesWidget(
                story: mockStory,
                onContinue: () {},
                narrationService: mockNarrationService,
              ),
            ),
          ),
        );

        // Should have page indicators for characters
        expect(find.byType(Container), findsWidgets);
      });

      testWidgets('should show Start Story button on last character', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: CharacterProfilesWidget(
                story: mockStory,
                onContinue: () {},
                narrationService: mockNarrationService,
              ),
            ),
          ),
        );

        // Wait for animations
        await tester.pumpAndSettle();

        expect(find.text('Start Story'), findsOneWidget);
      });
    });

    group('StorySceneWidget Tests', () {
      testWidgets('should display scene text and image', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: StorySceneWidget(
                story: mockStory,
                scene: mockStory.scenes.first,
                onChoiceSelected: (choice) {},
                onSceneComplete: () {},
                narrationService: mockNarrationService,
                settingsService: mockSettingsService,
              ),
            ),
          ),
        );

        expect(find.text('Once upon a time...'), findsOneWidget);
      });

      testWidgets('should show choices when scene has choices', (WidgetTester tester) async {
        final sceneWithChoices = mockStory.scenes[1]; // Second scene has choices

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: StorySceneWidget(
                story: mockStory,
                scene: sceneWithChoices,
                onChoiceSelected: (choice) {},
                onSceneComplete: () {},
                narrationService: mockNarrationService,
                settingsService: mockSettingsService,
              ),
            ),
          ),
        );

        // Wait for narration to complete and choices to appear
        await tester.pumpAndSettle(const Duration(seconds: 3));

        expect(find.text('Go left'), findsOneWidget);
        expect(find.text('Go right'), findsOneWidget);
      });

      testWidgets('should call onChoiceSelected when choice is tapped', (WidgetTester tester) async {
        final sceneWithChoices = mockStory.scenes[1];
        ChoiceOptionModel? selectedChoice;

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: StorySceneWidget(
                story: mockStory,
                scene: sceneWithChoices,
                onChoiceSelected: (choice) => selectedChoice = choice,
                onSceneComplete: () {},
                narrationService: mockNarrationService,
                settingsService: mockSettingsService,
              ),
            ),
          ),
        );

        // Wait for choices to appear
        await tester.pumpAndSettle(const Duration(seconds: 3));

        final leftChoice = find.text('Go left');
        if (leftChoice.evaluate().isNotEmpty) {
          await tester.tap(leftChoice);
          await tester.pump();

          expect(selectedChoice, isNotNull);
          expect(selectedChoice!.option, equals('Go left'));
        }
      });

      testWidgets('should show continue button for non-choice scenes', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: StorySceneWidget(
                story: mockStory,
                scene: mockStory.scenes.first,
                onChoiceSelected: (choice) {},
                onSceneComplete: () {},
                narrationService: mockNarrationService,
                settingsService: mockSettingsService,
              ),
            ),
          ),
        );

        // Wait for narration to complete
        await tester.pumpAndSettle(const Duration(seconds: 3));

        expect(find.text('Continue'), findsOneWidget);
      });

      testWidgets('should display emotion-based visual effects', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: StorySceneWidget(
                story: mockStory,
                scene: mockStory.scenes.first,
                onChoiceSelected: (choice) {},
                onSceneComplete: () {},
                narrationService: mockNarrationService,
                settingsService: mockSettingsService,
              ),
            ),
          ),
        );

        // The widget should render without errors
        expect(find.byType(StorySceneWidget), findsOneWidget);
      });
    });

    group('Responsive Design Tests', () {
      testWidgets('should adapt to mobile screen size', (WidgetTester tester) async {
        await tester.binding.setSurfaceSize(const Size(320, 568)); // iPhone SE size

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: WelcomeScreenWidget(
                story: mockStory,
                onContinue: () {},
                narrationService: mockNarrationService,
              ),
            ),
          ),
        );

        expect(find.byType(WelcomeScreenWidget), findsOneWidget);
        await tester.binding.setSurfaceSize(null);
      });

      testWidgets('should adapt to tablet screen size', (WidgetTester tester) async {
        await tester.binding.setSurfaceSize(const Size(768, 1024)); // iPad size

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: WelcomeScreenWidget(
                story: mockStory,
                onContinue: () {},
                narrationService: mockNarrationService,
              ),
            ),
          ),
        );

        expect(find.byType(WelcomeScreenWidget), findsOneWidget);
        await tester.binding.setSurfaceSize(null);
      });

      testWidgets('should adapt to desktop screen size', (WidgetTester tester) async {
        await tester.binding.setSurfaceSize(const Size(1200, 800)); // Desktop size

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: WelcomeScreenWidget(
                story: mockStory,
                onContinue: () {},
                narrationService: mockNarrationService,
              ),
            ),
          ),
        );

        expect(find.byType(WelcomeScreenWidget), findsOneWidget);
        await tester.binding.setSurfaceSize(null);
      });
    });

    group('Animation Tests', () {
      testWidgets('should animate welcome screen elements', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: WelcomeScreenWidget(
                story: mockStory,
                onContinue: () {},
                narrationService: mockNarrationService,
              ),
            ),
          ),
        );

        // Initially, elements might be animating in
        await tester.pump();
        
        // After animations complete, elements should be visible
        await tester.pumpAndSettle();
        
        expect(find.text('Test Story'), findsOneWidget);
      });

      testWidgets('should animate character profile transitions', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: CharacterProfilesWidget(
                story: mockStory,
                onContinue: () {},
                narrationService: mockNarrationService,
              ),
            ),
          ),
        );

        // Wait for initial animations
        await tester.pumpAndSettle();
        
        expect(find.text('Alice'), findsOneWidget);
      });
    });
  });
}
