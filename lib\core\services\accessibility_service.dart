import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:logger/logger.dart';

/// Service for managing accessibility features
class AccessibilityService {
  static final Logger _logger = Logger();
  static AccessibilityService? _instance;
  
  SharedPreferences? _prefs;
  
  // Settings keys
  static const String _highContrastKey = 'high_contrast_mode';
  static const String _largeTextKey = 'large_text_mode';
  static const String _reducedMotionKey = 'reduced_motion';
  static const String _voiceOverEnabledKey = 'voice_over_enabled';
  static const String _subtitleContrastKey = 'subtitle_contrast';
  static const String _focusIndicatorKey = 'enhanced_focus_indicator';
  
  // Default values
  static const bool _defaultHighContrast = false;
  static const bool _defaultLargeText = false;
  static const bool _defaultReducedMotion = false;
  static const bool _defaultVoiceOverEnabled = false;
  static const double _defaultSubtitleContrast = 0.8;
  static const bool _defaultFocusIndicator = true;
  
  // Current settings
  bool _highContrastMode = _defaultHighContrast;
  bool _largeTextMode = _defaultLargeText;
  bool _reducedMotion = _defaultReducedMotion;
  bool _voiceOverEnabled = _defaultVoiceOverEnabled;
  double _subtitleContrast = _defaultSubtitleContrast;
  bool _enhancedFocusIndicator = _defaultFocusIndicator;
  
  // Stream controllers for settings changes
  final StreamController<bool> _highContrastController = StreamController<bool>.broadcast();
  final StreamController<bool> _largeTextController = StreamController<bool>.broadcast();
  final StreamController<bool> _reducedMotionController = StreamController<bool>.broadcast();
  final StreamController<bool> _voiceOverController = StreamController<bool>.broadcast();
  final StreamController<double> _subtitleContrastController = StreamController<double>.broadcast();
  final StreamController<bool> _focusIndicatorController = StreamController<bool>.broadcast();
  
  // Private constructor
  AccessibilityService._();
  
  /// Get singleton instance
  static AccessibilityService get instance {
    _instance ??= AccessibilityService._();
    return _instance!;
  }
  
  // Getters for streams
  Stream<bool> get highContrastStream => _highContrastController.stream;
  Stream<bool> get largeTextStream => _largeTextController.stream;
  Stream<bool> get reducedMotionStream => _reducedMotionController.stream;
  Stream<bool> get voiceOverStream => _voiceOverController.stream;
  Stream<double> get subtitleContrastStream => _subtitleContrastController.stream;
  Stream<bool> get focusIndicatorStream => _focusIndicatorController.stream;
  
  // Getters for current values
  bool get highContrastMode => _highContrastMode;
  bool get largeTextMode => _largeTextMode;
  bool get reducedMotion => _reducedMotion;
  bool get voiceOverEnabled => _voiceOverEnabled;
  double get subtitleContrast => _subtitleContrast;
  bool get enhancedFocusIndicator => _enhancedFocusIndicator;

  /// Initialize the accessibility service
  Future<void> initialize() async {
    try {
      _logger.i('[AccessibilityService] Initializing accessibility service');
      
      _prefs = await SharedPreferences.getInstance();
      await _loadSettings();
      await _detectSystemAccessibilitySettings();
      
      _logger.i('[AccessibilityService] Accessibility service initialized successfully');
      
    } catch (e) {
      _logger.e('[AccessibilityService] Failed to initialize accessibility service: $e');
      _setDefaultValues();
    }
  }

  /// Load settings from SharedPreferences
  Future<void> _loadSettings() async {
    if (_prefs == null) return;
    
    _highContrastMode = _prefs!.getBool(_highContrastKey) ?? _defaultHighContrast;
    _largeTextMode = _prefs!.getBool(_largeTextKey) ?? _defaultLargeText;
    _reducedMotion = _prefs!.getBool(_reducedMotionKey) ?? _defaultReducedMotion;
    _voiceOverEnabled = _prefs!.getBool(_voiceOverEnabledKey) ?? _defaultVoiceOverEnabled;
    _subtitleContrast = _prefs!.getDouble(_subtitleContrastKey) ?? _defaultSubtitleContrast;
    _enhancedFocusIndicator = _prefs!.getBool(_focusIndicatorKey) ?? _defaultFocusIndicator;
    
    _logger.d('[AccessibilityService] Settings loaded - High contrast: $_highContrastMode, Large text: $_largeTextMode');
  }

  /// Detect system accessibility settings
  Future<void> _detectSystemAccessibilitySettings() async {
    try {
      // Check if we should automatically enable features based on system settings
      // This is a simplified implementation - in a real app, you might use platform channels
      // to detect actual system accessibility settings
      
      _logger.d('[AccessibilityService] System accessibility settings detected');
    } catch (e) {
      _logger.w('[AccessibilityService] Failed to detect system accessibility settings: $e');
    }
  }

  /// Set default values
  void _setDefaultValues() {
    _highContrastMode = _defaultHighContrast;
    _largeTextMode = _defaultLargeText;
    _reducedMotion = _defaultReducedMotion;
    _voiceOverEnabled = _defaultVoiceOverEnabled;
    _subtitleContrast = _defaultSubtitleContrast;
    _enhancedFocusIndicator = _defaultFocusIndicator;
  }

  /// Toggle high contrast mode
  Future<void> setHighContrastMode(bool enabled) async {
    if (_highContrastMode != enabled) {
      _highContrastMode = enabled;
      _highContrastController.add(_highContrastMode);
      
      await _saveHighContrastMode();
      _logger.d('[AccessibilityService] High contrast mode updated to: $_highContrastMode');
      
      // Provide haptic feedback
      if (enabled) {
        HapticFeedback.lightImpact();
      }
    }
  }

  /// Toggle large text mode
  Future<void> setLargeTextMode(bool enabled) async {
    if (_largeTextMode != enabled) {
      _largeTextMode = enabled;
      _largeTextController.add(_largeTextMode);
      
      await _saveLargeTextMode();
      _logger.d('[AccessibilityService] Large text mode updated to: $_largeTextMode');
      
      // Provide haptic feedback
      if (enabled) {
        HapticFeedback.lightImpact();
      }
    }
  }

  /// Toggle reduced motion
  Future<void> setReducedMotion(bool enabled) async {
    if (_reducedMotion != enabled) {
      _reducedMotion = enabled;
      _reducedMotionController.add(_reducedMotion);
      
      await _saveReducedMotion();
      _logger.d('[AccessibilityService] Reduced motion updated to: $_reducedMotion');
    }
  }

  /// Toggle voice over support
  Future<void> setVoiceOverEnabled(bool enabled) async {
    if (_voiceOverEnabled != enabled) {
      _voiceOverEnabled = enabled;
      _voiceOverController.add(_voiceOverEnabled);
      
      await _saveVoiceOverEnabled();
      _logger.d('[AccessibilityService] Voice over enabled updated to: $_voiceOverEnabled');
    }
  }

  /// Set subtitle contrast level (0.0 to 1.0)
  Future<void> setSubtitleContrast(double contrast) async {
    final clampedContrast = contrast.clamp(0.0, 1.0);
    
    if (_subtitleContrast != clampedContrast) {
      _subtitleContrast = clampedContrast;
      _subtitleContrastController.add(_subtitleContrast);
      
      await _saveSubtitleContrast();
      _logger.d('[AccessibilityService] Subtitle contrast updated to: $_subtitleContrast');
    }
  }

  /// Toggle enhanced focus indicator
  Future<void> setEnhancedFocusIndicator(bool enabled) async {
    if (_enhancedFocusIndicator != enabled) {
      _enhancedFocusIndicator = enabled;
      _focusIndicatorController.add(_enhancedFocusIndicator);
      
      await _saveFocusIndicator();
      _logger.d('[AccessibilityService] Enhanced focus indicator updated to: $_enhancedFocusIndicator');
    }
  }

  /// Get text scale factor based on large text setting
  double getTextScaleFactor() {
    return _largeTextMode ? 1.3 : 1.0;
  }

  /// Get contrast ratio for UI elements
  double getContrastRatio() {
    return _highContrastMode ? 1.0 : 0.87;
  }

  /// Get animation duration multiplier
  double getAnimationDurationMultiplier() {
    return _reducedMotion ? 0.0 : 1.0;
  }

  /// Get subtitle background opacity
  double getSubtitleBackgroundOpacity() {
    return _subtitleContrast;
  }

  /// Get semantic label for screen readers
  String getSemanticLabel(String text, {String? context}) {
    if (!_voiceOverEnabled) return text;
    
    // Enhanced semantic labels for screen readers
    if (context != null) {
      return '$context: $text';
    }
    return text;
  }

  /// Save settings to SharedPreferences
  Future<void> _saveHighContrastMode() async {
    try {
      await _prefs?.setBool(_highContrastKey, _highContrastMode);
    } catch (e) {
      _logger.e('[AccessibilityService] Failed to save high contrast mode: $e');
    }
  }

  Future<void> _saveLargeTextMode() async {
    try {
      await _prefs?.setBool(_largeTextKey, _largeTextMode);
    } catch (e) {
      _logger.e('[AccessibilityService] Failed to save large text mode: $e');
    }
  }

  Future<void> _saveReducedMotion() async {
    try {
      await _prefs?.setBool(_reducedMotionKey, _reducedMotion);
    } catch (e) {
      _logger.e('[AccessibilityService] Failed to save reduced motion: $e');
    }
  }

  Future<void> _saveVoiceOverEnabled() async {
    try {
      await _prefs?.setBool(_voiceOverEnabledKey, _voiceOverEnabled);
    } catch (e) {
      _logger.e('[AccessibilityService] Failed to save voice over enabled: $e');
    }
  }

  Future<void> _saveSubtitleContrast() async {
    try {
      await _prefs?.setDouble(_subtitleContrastKey, _subtitleContrast);
    } catch (e) {
      _logger.e('[AccessibilityService] Failed to save subtitle contrast: $e');
    }
  }

  Future<void> _saveFocusIndicator() async {
    try {
      await _prefs?.setBool(_focusIndicatorKey, _enhancedFocusIndicator);
    } catch (e) {
      _logger.e('[AccessibilityService] Failed to save focus indicator: $e');
    }
  }

  /// Reset all accessibility settings to defaults
  Future<void> resetToDefaults() async {
    _logger.i('[AccessibilityService] Resetting all accessibility settings to defaults');
    
    await setHighContrastMode(_defaultHighContrast);
    await setLargeTextMode(_defaultLargeText);
    await setReducedMotion(_defaultReducedMotion);
    await setVoiceOverEnabled(_defaultVoiceOverEnabled);
    await setSubtitleContrast(_defaultSubtitleContrast);
    await setEnhancedFocusIndicator(_defaultFocusIndicator);
    
    _logger.i('[AccessibilityService] All accessibility settings reset to defaults');
  }

  /// Get accessibility settings as a map for debugging
  Map<String, dynamic> getAccessibilitySettings() {
    return {
      'highContrastMode': _highContrastMode,
      'largeTextMode': _largeTextMode,
      'reducedMotion': _reducedMotion,
      'voiceOverEnabled': _voiceOverEnabled,
      'subtitleContrast': _subtitleContrast,
      'enhancedFocusIndicator': _enhancedFocusIndicator,
    };
  }

  /// Dispose of resources
  void dispose() {
    _highContrastController.close();
    _largeTextController.close();
    _reducedMotionController.close();
    _voiceOverController.close();
    _subtitleContrastController.close();
    _focusIndicatorController.close();
    _logger.i('[AccessibilityService] Service disposed');
  }
}
