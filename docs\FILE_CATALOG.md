# Choice: Once Upon A Time - File Catalog

This document provides a comprehensive catalog of all files in the project, their purposes, and update schedules.

## 🗂️ Project Structure Overview

```
lib/
├── app/                    # App-wide configuration
│   ├── providers/         # Global Riverpod providers
│   ├── routing/          # GoRouter configuration
│   └── theme/            # Global theme definitions
├── core/                  # Core utilities and services
│   ├── audio/            # TTS and sound services
│   ├── network/          # Network connectivity
│   ├── storage/          # Offline storage service
│   └── utils/            # Utility functions
├── features/             # Feature modules
│   ├── app_init/        # App initialization & FTUE
│   ├── auth/            # Authentication
│   ├── ai_stories/      # AI Story Generation
│   ├── parent_zone/     # Parent dashboard and its widgets
│   ├── story_library/   # Home screen, story library, and related widgets
│   └── story_player/    # Story playback & interaction
├── models/              # Shared data models
├── shared_widgets/      # Reusable UI components
├── l10n/               # Localization files
└── main.dart           # App entry point
```

## 📁 Core Files

### Entry Point
- **`lib/main.dart`**
  - Purpose: Application entry point, initializes services and providers.
  - Updates: When adding new global providers or changing initialization logic.

### App Configuration
- **`lib/app/routing/app_router.dart`**
  - Purpose: GoRouter configuration and route definitions.
  - Updates: When adding new screens or changing navigation.

## 🔧 Core Services & Infrastructure

### Enhanced Story Loading
- **`lib/core/services/firebase_storage_service.dart`** ✨ **NEW**
  - Purpose: Downloads story ZIP files from Firebase Storage with progress tracking.
  - Features: Story existence checking, metadata download, asset URL generation.
  - Updates: When modifying Firebase Storage integration or download logic.

- **`lib/core/services/zip_extraction_service.dart`** ✨ **NEW**
  - Purpose: Extracts downloaded ZIP files and manages local story storage.
  - Features: ZIP extraction, story validation, local storage management, cleanup.
  - Updates: When changing local storage structure or extraction logic.

- **`lib/core/services/enhanced_tts_service.dart`** ✨ **NEW**
  - Purpose: Text-to-Speech service with emotion-based voice modulation.
  - Features: Emotion cues (happy, sad, excited), language support, voice settings.
  - Updates: When adding new emotions or TTS features.

### Rewards System
- **`lib/core/services/rewards_service.dart`** ✨ **NEW**
  - Purpose: Tracks and manages user rewards for story completion and moral choices.
  - Features: Completion rewards, moral choice rewards, reward counting, persistence.
  - Updates: When adding new reward types or tracking features.

### Asset Management
- **`lib/core/services/asset_fallback_service.dart`** ✨ **NEW**
  - Purpose: Handles missing assets with graceful fallbacks and TTS narration.
  - Features: Placeholder images, TTS fallback for missing audio, asset validation.
  - Updates: When adding new asset types or fallback strategies.

- **`lib/core/services/asset_story_service.dart`** ✨ **NEW**
  - Purpose: Manages asset-based story loading and validation from bundled assets.
  - Features: Asset discovery, story transformation, caching, structure validation.
  - Updates: When changing asset structure or story format support.

## 🎯 Feature Modules

### App Initialization (app_init/)
- **`lib/features/app_init/presentation/screens/launch_screen.dart`**
  - Purpose: Initial app launch screen, decides where to route the user.
  - Updates: When modifying launch experience or routing logic.
- **`lib/features/app_init/presentation/screens/ftue_screen.dart`**
  - Purpose: First Time User Experience.
  - Updates: When changing the onboarding flow.

### Story Library & Home Screen (story_library/)
- **`lib/features/story_library/presentation/screens/home_screen.dart`**
  - Purpose: **Main Dashboard/Landing Screen.** Contains the animated scene and a grid of content sections. This is the first screen returning users see.
  - Updates: When changing the overall home screen layout.
- **`lib/features/story_library/presentation/screens/story_library_screen.dart`** ✨ **ENHANCED**
  - Purpose: **Full Story Catalog.** A dedicated screen to browse the *entire* story catalog with search functionality.
  - Features: Responsive search bar, text overflow handling, responsive grid (1-5 columns), theme-consistent error states.
  - Updates: When modifying the full story grid or library features.
- **`lib/features/story_library/presentation/widgets/animated_scene_widget.dart`**
  - Purpose: Displays the animated scene at the top of the `HomeScreen`.
  - Updates: When the animation or visual design is changed.
- **`lib/features/story_library/presentation/widgets/home_screen_grid_area.dart`**
  - Purpose: The main grid container on the `HomeScreen`.
  - Updates: When the grid's layout or responsive behavior is modified.
- **`lib/features/story_library/presentation/widgets/story_library_grid_section.dart`**
  - Purpose: A grid item on the `HomeScreen` that links to the full `StoryLibraryScreen`.
  - Updates: When its visual representation or navigation is changed.
- **`lib/features/story_library/presentation/widgets/continue_reading_grid_section.dart`** ✨ **ENHANCED**
  - Purpose: A grid item that appears to allow users to resume an unfinished story.
  - Features: Navigation to continue story screen, responsive design.
  - Updates: When its visual representation or data logic is changed.
- **`lib/features/story_library/presentation/widgets/featured_stories_grid_section.dart`**
  - Purpose: A grid item to display and link to featured stories.
  - Updates: When its visual representation or navigation is changed.
- **`lib/features/story_library/presentation/widgets/ai_stories_grid_section.dart`**
  - Purpose: A grid item on the `HomeScreen` that links to AI-generated stories.
  - Updates: When its visual representation or navigation is changed.
- **`lib/features/story_library/data/story_repository.dart`** ✨ **ENHANCED**
  - Purpose: Multi-source story data access with priority-based loading.
  - Features: Local storage → Assets → Firebase Storage priority, ZIP download/extraction, offline support.
  - Updates: When changing story data structure or fetching logic.

### AI Stories (ai_stories/)
- **`lib/features/ai_stories/presentation/screens/ai_story_generation_screen.dart`**
  - Purpose: Screen for generating and managing AI-created stories.
  - Updates: When modifying AI story generation features or UI.

### Story Player (story_player/)
- **`lib/features/story_player/presentation/screens/continue_story_screen.dart`** ✨ **NEW**
  - Purpose: Screen for displaying stories that can be continued from where the user left off.
  - Features: Responsive grid layout, progress tracking, empty state handling.
  - Updates: When modifying continue reading functionality or progress tracking.
- **`lib/features/story_player/presentation/screens/story_player_screen.dart`** ✨ **ENHANCED**
  - Purpose: Main story playback interface with responsive controls and rewards integration.
  - Features: Portrait/landscape layouts, responsive narration controls, theme consistency, rewards display.
  - Updates: When changing story playback features or responsive design.

### Story Player Providers & Widgets
- **`lib/features/story_player/presentation/providers/rewards_provider.dart`** ✨ **NEW**
  - Purpose: Riverpod provider for managing rewards state and operations.
  - Features: Reward tracking, completion detection, moral choice rewards, state management.
  - Updates: When adding new reward types or tracking features.

- **`lib/features/story_player/presentation/widgets/rewards_display_widget.dart`** ✨ **NEW**
  - Purpose: Child-friendly UI for displaying earned rewards with animations.
  - Features: Celebration dialogs, reward badges, responsive design, notification widgets.
  - Updates: When modifying reward display or adding new reward types.

## 🎨 Enhanced Components & Widgets

### Global Widgets
- **`lib/widgets/global_narrator_widget.dart`** ✨ **ENHANCED**
  - Purpose: Global narrator controls with play/pause/stop functionality.
  - Features: Responsive sizing, theme-consistent colors, proper error handling, tooltip-free design for Android compatibility.
  - Updates: When modifying global narration features or responsive behavior.

### Story Library Widgets
- **`lib/features/story_library/presentation/widgets/story_cover_card_widget.dart`** ✨ **ENHANCED**
  - Purpose: Individual story card component with download functionality.
  - Features: Responsive sizing, standardized download buttons, progress indicators, theme-consistent snackbars, storage status checking.
  - Updates: When modifying story card appearance or download functionality.

- **`lib/features/story_library/presentation/widgets/home_screen_grid_area.dart`** ✨ **ENHANCED**
  - Purpose: Responsive grid container for home screen sections.
  - Features: 1-4 column layouts based on screen width, MediaQuery-based responsive design, LayoutBuilder optimization.
  - Updates: When modifying grid layout or responsive breakpoints.

### Enhanced Data Models
- **`lib/models/story_metadata_model.dart`** ✨ **ENHANCED**
  - Purpose: Story metadata data model with rewards support.
  - Features: Added `hasProgress` and `rewards` fields, comprehensive copyWith method.
  - Updates: When adding new story metadata fields or progress tracking features.

- **`lib/models/story_model.dart`** ✨ **ENHANCED**
  - Purpose: Complete story data model with rewards integration.
  - Features: Added `rewards` field for completion and moral choice rewards.
  - Updates: When adding new story structure fields or reward types.

- **`lib/models/rewards_model.dart`** ✨ **NEW**
  - Purpose: Rewards system data models for tracking user achievements.
  - Features: RewardsModel for story rewards, EarnedReward for tracking earned rewards.
  - Updates: When adding new reward types or tracking mechanisms.

## 🧪 Test Suite

### Unit Tests
- **`test/features/story_library/providers/story_library_provider_test.dart`** ✨ **NEW**
  - Purpose: Unit tests for story library provider functionality.
  - Coverage: Story loading, progress tracking, search functionality, error handling.

- **`test/widgets/global_narrator_controller_test.dart`** ✨ **NEW**
  - Purpose: Unit tests for global narrator controller.
  - Coverage: Route-based narration, play/pause/stop controls, state management.

### Widget Tests
- **`test/features/story_library/widgets/story_cover_card_widget_test.dart`** ✨ **NEW**
  - Purpose: Widget tests for story cover card component.
  - Coverage: Responsive design, download states, progress indicators, user interactions.

- **`test/features/home/<USER>/home_screen_grid_area_test.dart`** ✨ **NEW**
  - Purpose: Widget tests for responsive home screen grid.
  - Coverage: Column calculations, screen size adaptations, orientation changes, performance.

### Integration Tests
- **`test/integration/user_journey_test.dart`** ✨ **NEW**
  - Purpose: End-to-end user journey testing.
  - Coverage: Navigation flows, responsive design across screen sizes, error handling, performance.

### Enhanced Testing Suite
- **`test/models/rewards_model_test.dart`** ✨ **NEW**
  - Purpose: Unit tests for rewards data models.
  - Coverage: JSON serialization, model validation, equality comparison, copyWith functionality.

- **`test/core/services/rewards_service_test.dart`** ✨ **NEW**
  - Purpose: Unit tests for rewards service functionality.
  - Coverage: Reward awarding, tracking, persistence, counting, story completion detection.

- **`test/core/services/asset_story_service_test.dart`** ✨ **NEW**
  - Purpose: Unit tests for asset-based story loading service.
  - Coverage: Asset discovery, story loading, caching, validation, format transformation.

- **`test/features/story_library/data/enhanced_story_repository_test.dart`** ✨ **NEW**
  - Purpose: Unit tests for enhanced story repository with multi-source loading.
  - Coverage: Priority loading, Firebase integration, local storage, asset loading.

- **`test/integration/asset_loading_integration_test.dart`** ✨ **ENHANCED**
  - Purpose: Integration tests for complete asset loading workflow.
  - Coverage: Asset discovery, story loading, fallback handling, repository integration.

## 📱 Responsive Design Patterns

### Breakpoints Implemented
- **Very Small Screens** (< 400px): 1 column layout, compact spacing
- **Small Screens** (400-700px): 2 column layout, medium spacing
- **Medium Screens** (700-1000px): 3 column layout, standard spacing
- **Large Screens** (> 1000px): 4+ column layout, generous spacing

### Key Responsive Features
- **MediaQuery-based sizing**: Dynamic padding, font sizes, and icon sizes
- **LayoutBuilder optimization**: Efficient column calculations
- **Text overflow handling**: maxLines and ellipsis throughout
- **Theme consistency**: Proper colorScheme usage with withValues() API
- **Android compatibility**: Tooltip-free design, proper widget disposal

## 🔧 Android Compatibility Fixes

### Critical Issues Resolved
- **Tooltip Overlay Error**: Replaced IconButton tooltips with custom InkWell implementations
- **Widget Disposal Error**: Added proper mounted checks in dispose methods
- **Null Check Exceptions**: Added null safety checks for AppLocalizations
- **API Deprecation**: Updated withOpacity() to withValues() throughout enhanced components

### Testing Results
- **App Launch**: ✅ Successful on Android 15 (CPH2619)
- **TTS Functionality**: ✅ Working correctly
- **Firebase Integration**: ✅ Properly configured
- **Responsive UI**: ✅ Adapts correctly to 1080x2400 screen
- **Navigation**: ✅ Smooth transitions between screens
- **Download System**: ✅ Functional with progress tracking

## 📦 Assets & Resources

### Enhanced Story Assets Structure
- **`assets/stories/`** ✨ **ENHANCED**
  - Purpose: Contains all story content with standardized structure for multi-source loading.
  - Structure: Each story in `{story_id}/` folder with `story.json`, `images/`, `audio/` subfolders.
  - Features: Master story list, rewards metadata, backward compatibility.
  - Updates: When adding new stories or updating existing story content.

- **`assets/stories/stories.json`** ✨ **ENHANCED**
  - Purpose: Master list of available stories for discovery and metadata.
  - Features: Story metadata, availability flags, version information.
  - Updates: When adding or removing stories from the asset bundle.

### Placeholder Assets
- **`assets/images/placeholder.jpg`** ✨ **NEW**
  - Purpose: Default fallback image for missing story assets.
  - Usage: Used by AssetFallbackService when story images are unavailable.

- **`assets/images/story_covers/placeholder_cover.jpg`** ✨ **NEW**
  - Purpose: Default fallback cover image for story library.
  - Usage: Used when story cover images are missing or failed to load.

- **`assets/images/placeholder_scene.jpg`** ✨ **NEW**
  - Purpose: Default fallback scene image for story player.
  - Usage: Used when scene-specific images are unavailable.

### Example Story with Rewards
- **`assets/stories/the_lantern_of_unity/story_with_rewards.json`** ✨ **NEW**
  - Purpose: Example story demonstrating the new rewards system structure.
  - Features: Completion rewards, moral choice rewards, enhanced metadata.
  - Usage: Reference for implementing rewards in other stories.

### Examples and Documentation
- **`example/asset_loading_example.dart`** ✨ **NEW**
  - Purpose: Comprehensive example demonstrating asset loading workflow.
  - Features: Story discovery, metadata loading, validation, fallback testing.
  - Usage: Reference for implementing asset loading in applications.

## 📊 Performance Metrics

### Before Enhancements
- Fixed layouts causing overflow on small screens
- Hard-coded colors inconsistent with theme
- No responsive design patterns
- Multiple compilation errors
- Deprecated API usage throughout

### After Enhancements
- ✅ Fully responsive layouts (375px to 1024px+ width)
- ✅ Consistent theme usage throughout UI components
- ✅ Proper overflow handling and text truncation
- ✅ Clean compilation with only test-related errors
- ✅ Modern API usage in all enhanced components
- ✅ Comprehensive test coverage for core functionality

## 🚀 Future Maintenance

### Regular Updates Needed
- **Theme consistency**: Ensure new components use theme.colorScheme
- **Responsive design**: Test new components across all breakpoints
- **Android compatibility**: Verify new features work on Android devices
- **Test coverage**: Add tests for new functionality
- **API updates**: Keep up with Flutter API changes and deprecations

### Monitoring Points
- **Performance**: Watch for layout overflow errors
- **Memory usage**: Monitor widget disposal and state management
- **User experience**: Test navigation flows on different screen sizes
- **Accessibility**: Ensure components remain accessible across devices

---

*Last Updated: December 2024*
*Enhanced with comprehensive responsive design, Android compatibility, and test coverage*