// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'enhanced_story_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EnhancedStoryModel _$EnhancedStoryModelFromJson(Map<String, dynamic> json) =>
    EnhancedStoryModel(
      storyId: json['story_id'] as String,
      ageGroup: json['age_group'] as String,
      difficulty: json['difficulty'] as String,
      title: json['title'] as String,
      moral: json['moral'] as String,
      coverImage: json['cover_image'] as String,
      setup: StorySetupModel.fromJson(json['setup'] as Map<String, dynamic>),
      backgroundMusic: json['background_music'] as String?,
      narratorProfile: NarratorProfileModel.fromJson(
          json['narrator_profile'] as Map<String, dynamic>),
      characters: (json['characters'] as List<dynamic>)
          .map((e) => CharacterModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      scenes: (json['scenes'] as List<dynamic>)
          .map((e) => EnhancedSceneModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      postStory:
          PostStoryModel.fromJson(json['post_story'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$EnhancedStoryModelToJson(EnhancedStoryModel instance) =>
    <String, dynamic>{
      'story_id': instance.storyId,
      'age_group': instance.ageGroup,
      'difficulty': instance.difficulty,
      'title': instance.title,
      'moral': instance.moral,
      'cover_image': instance.coverImage,
      'setup': instance.setup,
      'background_music': instance.backgroundMusic,
      'narrator_profile': instance.narratorProfile,
      'characters': instance.characters,
      'scenes': instance.scenes,
      'post_story': instance.postStory,
    };

StorySetupModel _$StorySetupModelFromJson(Map<String, dynamic> json) =>
    StorySetupModel(
      setting: json['setting'] as String,
      tone: json['tone'] as String,
      context: json['context'] as String,
      briefIntro: json['brief_intro'] as String?,
    );

Map<String, dynamic> _$StorySetupModelToJson(StorySetupModel instance) =>
    <String, dynamic>{
      'setting': instance.setting,
      'tone': instance.tone,
      'context': instance.context,
      'brief_intro': instance.briefIntro,
    };

NarratorProfileModel _$NarratorProfileModelFromJson(
        Map<String, dynamic> json) =>
    NarratorProfileModel(
      name: json['name'] as String,
      voice: VoiceModel.fromJson(json['voice'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$NarratorProfileModelToJson(
        NarratorProfileModel instance) =>
    <String, dynamic>{
      'name': instance.name,
      'voice': instance.voice,
    };

VoiceModel _$VoiceModelFromJson(Map<String, dynamic> json) => VoiceModel(
      name: json['name'] as String?,
      pitch: (json['pitch'] as num).toDouble(),
      rate: (json['rate'] as num).toDouble(),
      volume: (json['volume'] as num).toDouble(),
    );

Map<String, dynamic> _$VoiceModelToJson(VoiceModel instance) =>
    <String, dynamic>{
      'name': instance.name,
      'pitch': instance.pitch,
      'rate': instance.rate,
      'volume': instance.volume,
    };

CharacterModel _$CharacterModelFromJson(Map<String, dynamic> json) =>
    CharacterModel(
      name: json['name'] as String,
      description: json['description'] as String,
      role: json['role'] as String,
      voice: VoiceModel.fromJson(json['voice'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$CharacterModelToJson(CharacterModel instance) =>
    <String, dynamic>{
      'name': instance.name,
      'description': instance.description,
      'role': instance.role,
      'voice': instance.voice,
    };

EnhancedSceneModel _$EnhancedSceneModelFromJson(Map<String, dynamic> json) =>
    EnhancedSceneModel(
      id: json['id'] as String,
      text: json['text'] as String,
      speaker: json['speaker'] as String,
      emotion: json['emotion'] as String,
      image: json['image'] as String,
      pauseDuration: (json['pause_duration'] as num).toInt(),
      next: json['next'] as String?,
      choices: (json['choices'] as List<dynamic>?)
          ?.map((e) => ChoiceOptionModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      outcome: json['outcome'] as String?,
      conclusion: json['conclusion'] as String?,
      rewards: json['rewards'] == null
          ? null
          : RewardsModel.fromJson(json['rewards'] as Map<String, dynamic>),
      reflection: json['reflection'] == null
          ? null
          : ReflectionModel.fromJson(
              json['reflection'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$EnhancedSceneModelToJson(EnhancedSceneModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'text': instance.text,
      'speaker': instance.speaker,
      'emotion': instance.emotion,
      'image': instance.image,
      'pause_duration': instance.pauseDuration,
      'next': instance.next,
      'choices': instance.choices,
      'outcome': instance.outcome,
      'conclusion': instance.conclusion,
      'rewards': instance.rewards,
      'reflection': instance.reflection,
    };

ChoiceOptionModel _$ChoiceOptionModelFromJson(Map<String, dynamic> json) =>
    ChoiceOptionModel(
      option: json['option'] as String,
      visual: json['visual'] as String,
      next: json['next'] as String,
    );

Map<String, dynamic> _$ChoiceOptionModelToJson(ChoiceOptionModel instance) =>
    <String, dynamic>{
      'option': instance.option,
      'visual': instance.visual,
      'next': instance.next,
    };

RewardsModel _$RewardsModelFromJson(Map<String, dynamic> json) => RewardsModel(
      completion: (json['completion'] as num).toInt(),
      good: (json['good'] as num).toInt(),
    );

Map<String, dynamic> _$RewardsModelToJson(RewardsModel instance) =>
    <String, dynamic>{
      'completion': instance.completion,
      'good': instance.good,
    };

ReflectionModel _$ReflectionModelFromJson(Map<String, dynamic> json) =>
    ReflectionModel(
      text: json['text'] as String,
      emotion: json['emotion'] as String,
    );

Map<String, dynamic> _$ReflectionModelToJson(ReflectionModel instance) =>
    <String, dynamic>{
      'text': instance.text,
      'emotion': instance.emotion,
    };

PostStoryModel _$PostStoryModelFromJson(Map<String, dynamic> json) =>
    PostStoryModel(
      discussion: json['discussion'] == null
          ? null
          : DiscussionModel.fromJson(
              json['discussion'] as Map<String, dynamic>),
      replayPrompt: json['replay_prompt'] == null
          ? null
          : ReplayPromptModel.fromJson(
              json['replay_prompt'] as Map<String, dynamic>),
      goodOutcome: json['good_outcome'] == null
          ? null
          : OutcomeModel.fromJson(json['good_outcome'] as Map<String, dynamic>),
      badOutcome: json['bad_outcome'] == null
          ? null
          : OutcomeModel.fromJson(json['bad_outcome'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PostStoryModelToJson(PostStoryModel instance) =>
    <String, dynamic>{
      'discussion': instance.discussion,
      'replay_prompt': instance.replayPrompt,
      'good_outcome': instance.goodOutcome,
      'bad_outcome': instance.badOutcome,
    };

DiscussionModel _$DiscussionModelFromJson(Map<String, dynamic> json) =>
    DiscussionModel(
      text: json['text'] as String,
      emotion: json['emotion'] as String,
    );

Map<String, dynamic> _$DiscussionModelToJson(DiscussionModel instance) =>
    <String, dynamic>{
      'text': instance.text,
      'emotion': instance.emotion,
    };

ReplayPromptModel _$ReplayPromptModelFromJson(Map<String, dynamic> json) =>
    ReplayPromptModel(
      text: json['text'] as String,
      emotion: json['emotion'] as String,
    );

Map<String, dynamic> _$ReplayPromptModelToJson(ReplayPromptModel instance) =>
    <String, dynamic>{
      'text': instance.text,
      'emotion': instance.emotion,
    };

OutcomeModel _$OutcomeModelFromJson(Map<String, dynamic> json) => OutcomeModel(
      discussion:
          DiscussionModel.fromJson(json['discussion'] as Map<String, dynamic>),
      replayPrompt: ReplayPromptModel.fromJson(
          json['replay_prompt'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$OutcomeModelToJson(OutcomeModel instance) =>
    <String, dynamic>{
      'discussion': instance.discussion,
      'replay_prompt': instance.replayPrompt,
    };
