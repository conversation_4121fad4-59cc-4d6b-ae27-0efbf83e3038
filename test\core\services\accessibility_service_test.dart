import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:choice_once_upon_a_time/core/services/accessibility_service.dart';

void main() {
  group('AccessibilityService Tests', () {
    late AccessibilityService service;

    setUp(() async {
      // Clear shared preferences before each test
      SharedPreferences.setMockInitialValues({});
      service = AccessibilityService.instance;
      await service.initialize();
    });

    tearDown(() async {
      await service.resetToDefaults();
    });

    group('Initialization', () {
      test('should initialize with default values', () async {
        expect(service.highContrastMode, isFalse);
        expect(service.largeTextMode, isFalse);
        expect(service.reducedMotion, isFalse);
        expect(service.voiceOverEnabled, isFalse);
        expect(service.subtitleContrast, equals(0.8));
        expect(service.enhancedFocusIndicator, isTrue);
      });

      test('should load saved preferences on initialization', () async {
        // Set up mock preferences
        SharedPreferences.setMockInitialValues({
          'high_contrast_mode': true,
          'large_text_mode': true,
          'reduced_motion': false,
          'voice_over_enabled': true,
          'subtitle_contrast': 0.9,
          'enhanced_focus_indicator': false,
        });

        final newService = AccessibilityService.instance;
        await newService.initialize();

        expect(newService.highContrastMode, isTrue);
        expect(newService.largeTextMode, isTrue);
        expect(newService.reducedMotion, isFalse);
        expect(newService.voiceOverEnabled, isTrue);
        expect(newService.subtitleContrast, equals(0.9));
        expect(newService.enhancedFocusIndicator, isFalse);
      });
    });

    group('High Contrast Mode', () {
      test('should toggle high contrast mode', () async {
        expect(service.highContrastMode, isFalse);

        await service.setHighContrastMode(true);
        expect(service.highContrastMode, isTrue);

        await service.setHighContrastMode(false);
        expect(service.highContrastMode, isFalse);
      });

      test('should emit stream events when high contrast changes', () async {
        final stream = service.highContrastStream;
        final events = <bool>[];

        final subscription = stream.listen(events.add);

        await service.setHighContrastMode(true);
        await service.setHighContrastMode(false);

        await Future.delayed(const Duration(milliseconds: 10));
        subscription.cancel();

        expect(events, equals([true, false]));
      });

      test('should persist high contrast mode setting', () async {
        await service.setHighContrastMode(true);

        final prefs = await SharedPreferences.getInstance();
        expect(prefs.getBool('high_contrast_mode'), isTrue);
      });
    });

    group('Large Text Mode', () {
      test('should toggle large text mode', () async {
        expect(service.largeTextMode, isFalse);

        await service.setLargeTextMode(true);
        expect(service.largeTextMode, isTrue);

        await service.setLargeTextMode(false);
        expect(service.largeTextMode, isFalse);
      });

      test('should emit stream events when large text changes', () async {
        final stream = service.largeTextStream;
        final events = <bool>[];

        final subscription = stream.listen(events.add);

        await service.setLargeTextMode(true);
        await service.setLargeTextMode(false);

        await Future.delayed(const Duration(milliseconds: 10));
        subscription.cancel();

        expect(events, equals([true, false]));
      });

      test('should return correct text scale factor', () {
        expect(service.getTextScaleFactor(), equals(1.0));

        service.setLargeTextMode(true);
        expect(service.getTextScaleFactor(), equals(1.3));
      });
    });

    group('Reduced Motion', () {
      test('should toggle reduced motion', () async {
        expect(service.reducedMotion, isFalse);

        await service.setReducedMotion(true);
        expect(service.reducedMotion, isTrue);

        await service.setReducedMotion(false);
        expect(service.reducedMotion, isFalse);
      });

      test('should return correct animation duration multiplier', () {
        expect(service.getAnimationDurationMultiplier(), equals(1.0));

        service.setReducedMotion(true);
        expect(service.getAnimationDurationMultiplier(), equals(0.0));
      });
    });

    group('Voice Over Support', () {
      test('should toggle voice over enabled', () async {
        expect(service.voiceOverEnabled, isFalse);

        await service.setVoiceOverEnabled(true);
        expect(service.voiceOverEnabled, isTrue);

        await service.setVoiceOverEnabled(false);
        expect(service.voiceOverEnabled, isFalse);
      });

      test('should provide enhanced semantic labels when voice over is enabled', () {
        const text = 'Hello world';
        const context = 'Button';

        // Voice over disabled
        expect(service.getSemanticLabel(text), equals(text));
        expect(service.getSemanticLabel(text, context: context), equals(text));

        // Voice over enabled
        service.setVoiceOverEnabled(true);
        expect(service.getSemanticLabel(text), equals(text));
        expect(service.getSemanticLabel(text, context: context), equals('$context: $text'));
      });
    });

    group('Subtitle Contrast', () {
      test('should set subtitle contrast within valid range', () async {
        await service.setSubtitleContrast(0.5);
        expect(service.subtitleContrast, equals(0.5));

        await service.setSubtitleContrast(1.0);
        expect(service.subtitleContrast, equals(1.0));

        await service.setSubtitleContrast(0.0);
        expect(service.subtitleContrast, equals(0.0));
      });

      test('should clamp subtitle contrast to valid range', () async {
        await service.setSubtitleContrast(-0.5);
        expect(service.subtitleContrast, equals(0.0));

        await service.setSubtitleContrast(1.5);
        expect(service.subtitleContrast, equals(1.0));
      });

      test('should return subtitle background opacity', () {
        service.setSubtitleContrast(0.7);
        expect(service.getSubtitleBackgroundOpacity(), equals(0.7));
      });
    });

    group('Enhanced Focus Indicator', () {
      test('should toggle enhanced focus indicator', () async {
        expect(service.enhancedFocusIndicator, isTrue);

        await service.setEnhancedFocusIndicator(false);
        expect(service.enhancedFocusIndicator, isFalse);

        await service.setEnhancedFocusIndicator(true);
        expect(service.enhancedFocusIndicator, isTrue);
      });
    });

    group('Contrast Ratio', () {
      test('should return correct contrast ratio', () {
        expect(service.getContrastRatio(), equals(0.87));

        service.setHighContrastMode(true);
        expect(service.getContrastRatio(), equals(1.0));
      });
    });

    group('Settings Management', () {
      test('should reset all settings to defaults', () async {
        // Change all settings
        await service.setHighContrastMode(true);
        await service.setLargeTextMode(true);
        await service.setReducedMotion(true);
        await service.setVoiceOverEnabled(true);
        await service.setSubtitleContrast(0.5);
        await service.setEnhancedFocusIndicator(false);

        // Reset to defaults
        await service.resetToDefaults();

        expect(service.highContrastMode, isFalse);
        expect(service.largeTextMode, isFalse);
        expect(service.reducedMotion, isFalse);
        expect(service.voiceOverEnabled, isFalse);
        expect(service.subtitleContrast, equals(0.8));
        expect(service.enhancedFocusIndicator, isTrue);
      });

      test('should provide accessibility settings as map', () {
        final settings = service.getAccessibilitySettings();

        expect(settings, isA<Map<String, dynamic>>());
        expect(settings, containsPair('highContrastMode', isA<bool>()));
        expect(settings, containsPair('largeTextMode', isA<bool>()));
        expect(settings, containsPair('reducedMotion', isA<bool>()));
        expect(settings, containsPair('voiceOverEnabled', isA<bool>()));
        expect(settings, containsPair('subtitleContrast', isA<double>()));
        expect(settings, containsPair('enhancedFocusIndicator', isA<bool>()));
      });
    });

    group('Stream Behavior', () {
      test('should not emit duplicate values', () async {
        final stream = service.highContrastStream;
        final events = <bool>[];

        final subscription = stream.listen(events.add);

        // Set same value multiple times
        await service.setHighContrastMode(true);
        await service.setHighContrastMode(true);
        await service.setHighContrastMode(true);

        await Future.delayed(const Duration(milliseconds: 10));
        subscription.cancel();

        expect(events, equals([true]));
      });

      test('should emit events for all setting changes', () async {
        final highContrastEvents = <bool>[];
        final largeTextEvents = <bool>[];
        final reducedMotionEvents = <bool>[];

        final subscriptions = [
          service.highContrastStream.listen(highContrastEvents.add),
          service.largeTextStream.listen(largeTextEvents.add),
          service.reducedMotionStream.listen(reducedMotionEvents.add),
        ];

        await service.setHighContrastMode(true);
        await service.setLargeTextMode(true);
        await service.setReducedMotion(true);

        await Future.delayed(const Duration(milliseconds: 10));
        
        for (final subscription in subscriptions) {
          subscription.cancel();
        }

        expect(highContrastEvents, equals([true]));
        expect(largeTextEvents, equals([true]));
        expect(reducedMotionEvents, equals([true]));
      });
    });
  });
}
